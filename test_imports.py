#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify core functionality of architectural improvements
"""

import sys
import os
import pandas as pd
from datetime import datetime

# Test construct_sperandeo_data_context function signature
def test_construct_sperandeo_data_context():
    try:
        # Create mock data
        spy_ohlcv = pd.DataFrame({
            'Open': [450, 451, 452],
            'High': [452, 453, 454], 
            'Low': [449, 450, 451],
            'Close': [451, 452, 453],
            'Volume': [1000000, 1100000, 1200000]
        })
        
        sperandeo_features = pd.DataFrame({
            'trend_state': [1.0],
            '123_reversal_state': [0.0],
            '2b_signal': [0.0],
            '2b_strength': [0.0],
            'four_day_signal': [0.0],
            'trendline_break': [0.0],
            'consecutive_days': [3.0]
        })
        
        market_data = pd.DataFrame({
            '^VIX': [20.5],
            '^IRX': [4.5]
        })
        
        # Test function call with new signature (no fred_data parameter)
        exec(open('Option Colab.py').read())
        
        # This should work with the new signature
        result = construct_sperandeo_data_context(spy_ohlcv, sperandeo_features, market_data)
        
        print("✅ construct_sperandeo_data_context test passed")
        print(f"   Result length: {len(result)} characters")
        return True

    except Exception as e:
        print(f"❌ construct_sperandeo_data_context test failed: {e}")
        return False

# Test EnhancedFundManager instantiation
def test_enhanced_fund_manager():
    try:
        exec(open('Option Colab.py').read())
        
        # Test EnhancedFundManager creation
        fund_manager = EnhancedFundManager("test_portfolio.txt")
        
        print("✅ EnhancedFundManager instantiation test passed")
        print(f"   Portfolio file: {fund_manager.portfolio_file}")
        print(f"   Account size: ${fund_manager.account_size:,.0f}")
        print(f"   Max risk: {fund_manager.max_risk_pct:.1%}")
        return True

    except Exception as e:
        print(f"❌ EnhancedFundManager test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing TradingAgents Architectural Improvements")
    print("=" * 50)
    
    # Run tests
    test1_passed = test_construct_sperandeo_data_context()
    test2_passed = test_enhanced_fund_manager()
    
    print("\n" + "=" * 50)
    if test1_passed and test2_passed:
        print("✅ All core functionality tests passed!")
    else:
        print("❌ Some tests failed - check implementation")
    
    print("🎯 Architectural improvements validation complete")
