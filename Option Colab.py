# TradingAgents: Multi-Agent LLM Options Trading Framework
#
# Version: 2.0
# Description: This script implements an enhanced multi-agent LLM system for simulating SPY ETF options trading decisions.
# It features specialized analyst roles, including a quantitative-style option pricing analyst, and incorporates
# portfolio state awareness into the final trading decision.
#
# Author: AI Systems Architect & Quantitative Developer
# Date: 2024-05-17
#

import os
import re
import json
import math
import logging
import time
import requests
import traceback
from functools import wraps
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import pandas as pd
import numpy as np
import yfinance as yf
import pandas_datareader.data as web
from fredapi import Fred
from scipy.stats import norm

# OpenRouter API will be accessed via requests

# Enhanced Error Handling Components
@dataclass
class ErrorContext:
    """Context information for error handling"""
    function_name: str
    timestamp: datetime
    error_type: str
    error_message: str
    retry_count: int = 0
    fallback_used: bool = False
    recovery_action: Optional[str] = None

class CircuitBreaker:
    """Circuit breaker pattern for API calls"""

    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN

    def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection"""
        if self.state == "OPEN":
            if self._should_attempt_reset():
                self.state = "HALF_OPEN"
            else:
                raise Exception(f"Circuit breaker is OPEN. Service unavailable.")

        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise e

    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset"""
        if self.last_failure_time is None:
            return True
        return (datetime.now() - self.last_failure_time).seconds > self.recovery_timeout

    def _on_success(self):
        """Handle successful call"""
        self.failure_count = 0
        self.state = "CLOSED"

    def _on_failure(self):
        """Handle failed call"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()

        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"

def with_retry(max_retries: int = 3, backoff_factor: float = 2.0,
               exceptions: tuple = (Exception,)):
    """Decorator for retry logic with exponential backoff"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        wait_time = backoff_factor ** attempt
                        logging.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {e}. Retrying in {wait_time}s...")
                        time.sleep(wait_time)
                    else:
                        logging.error(f"All {max_retries + 1} attempts failed for {func.__name__}")

            raise last_exception
        return wrapper
    return decorator

# Global circuit breakers for different services
api_circuit_breakers = {
    'openrouter': CircuitBreaker(failure_threshold=3, recovery_timeout=300),
    'fred': CircuitBreaker(failure_threshold=5, recovery_timeout=600),
    'alpha_vantage': CircuitBreaker(failure_threshold=3, recovery_timeout=300),
    'yfinance': CircuitBreaker(failure_threshold=5, recovery_timeout=60)
}

# Pandas configuration for Sperandeo functions
pd.options.mode.chained_assignment = None  # silence chained assignment warnings
pd.set_option('future.no_silent_downcasting', True)  # suppress downcasting warnings

# --- CONFIGURATION ---

# API Keys
OPENROUTER_API_KEY = "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"
FRED_API_KEY = "dac90176cd11cbf8281068404bc30b7a"

ALPHAVANTAGE_API_KEY = os.getenv("ALPHAVANTAGE_API_KEY", "LBSPY17KXDHGHCPT")

# LLM Configuration - Strategic model selection as per paper
DEEP_THINKING_MODEL = "x-ai/grok-4-fast:free"  # For analysis tasks
QUICK_THINKING_MODEL = "x-ai/grok-4-fast:free"  # Would use gpt-4o-mini in production
DEBATE_MODEL = "x-ai/grok-4-fast:free"

# Simulation Parameters
ANALYSIS_WINDOW_DAYS = 126
HV_WINDOW = 21
DTE_CHOICES = [5, 8, 15, 22, 29, 36, 43, 57, 64, 85, 97, 113, 127]
DEBATE_ROUNDS = 3  # Number of debate rounds

# 4. Data Tickers and Series
MARKET_TICKERS = ["SPY", "^VIX", "^IRX"] # SPY ETF, VIX Index, 13-week T-bill (^IRX as risk-free rate)
FRED_SERIES = {
    "T10Y2Y": "10-Year Minus 2-Year Treasury Yield Spread",
    "USEPUINDXD": "Economic Policy Uncertainty Index for United States",
    "ADPWNUSNERSA": "Total Nonfarm Private Payroll Employment",
    "EMVOVERALLEMV": "Equity Market Volatility Tracker: Overall",
    "ICSA": "Initial Claims",
    "UMCSENT": "University of Michigan: Consumer Sentiment",
    "FEDFUNDS": "Federal Funds Effective Rate"
}

def normalize_fred_indicators(
    api_key: str,
    lookback_periods: int = 24,  # months for historical context
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Fetch FRED API data and normalize indicators to -1 to 1 scale based on 3-month average.
    Handles different data frequencies (daily, weekly, monthly).

    Parameters:
    -----------
    api_key : str
        FRED API key
    lookback_periods : int
        Number of months to use for normalization context (default: 24)
    start_date : str, optional
        Start date for data fetch (format: 'YYYY-MM-DD')
    end_date : str, optional
        End date for data fetch (format: 'YYYY-MM-DD')

    Returns:
    --------
    Tuple[pd.DataFrame, pd.DataFrame]
        - DataFrame with daily aligned data and 3-month averages
        - DataFrame with normalized indicators
    """

    # Initialize FRED API
    fred = Fred(api_key=api_key)

    # Define indicators with their CORRECT properties and frequencies
    indicators = {
        "T10Y2Y": {
            "name": "10-Year Minus 2-Year Treasury Yield Spread",
            "inverse": False,  # Higher spread is generally positive
            "frequency": "daily"
        },
        "USEPUINDXD": {
            "name": "Economic Policy Uncertainty Index",
            "inverse": True,  # Higher uncertainty is negative
            "frequency": "daily"
        },
        "ADPWNUSNERSA": {
            "name": "Total Nonfarm Private Payroll Employment",
            "inverse": False,  # Higher employment is positive
            "frequency": "monthly",
            "transform": "pct_change"  # Use percent change for employment levels
        },
        "EMVOVERALLEMV": {
            "name": "Equity Market Volatility Tracker",
            "inverse": True,  # Higher volatility is negative
            "frequency": "monthly"  # CORRECTED: This is monthly data
        },
        "ICSA": {
            "name": "Initial Claims",
            "inverse": True,  # Higher claims is negative
            "frequency": "weekly"
        },
        "UMCSENT": {
            "name": "Consumer Sentiment",
            "inverse": False,  # Higher sentiment is positive
            "frequency": "monthly"
        },
        "FEDFUNDS": {
            "name": "Federal Funds Effective Rate",
            "inverse": True,  # Higher rates are restrictive (negative for markets)
            "frequency": "monthly"
        }
    }

    # Set date range with extra buffer for rolling calculations
    if end_date is None:
        end_date = datetime.now().strftime('%Y-%m-%d')
    if start_date is None:
        # Fetch extra data for rolling calculations and normalization
        start_date = (datetime.now() - timedelta(days=(lookback_periods + 6) * 30)).strftime('%Y-%m-%d')

    # Store all series with their original frequencies
    raw_data = {}
    processed_data = {}

    print("Fetching and processing FRED data...")
    print("-" * 50)

    for symbol, props in indicators.items():
        try:
            # Fetch data from FRED
            series = fred.get_series(symbol, start_date, end_date)

            if series is None or len(series) == 0:
                print(f"Warning: No data retrieved for {symbol}")
                continue

            # Convert to DataFrame
            df = pd.DataFrame(series, columns=[symbol])

            # Store raw data
            raw_data[symbol] = df.copy()

            # Process based on data frequency and characteristics
            processed = process_series_by_frequency(
                df[symbol],
                symbol,
                props,
                target_freq='D'  # Resample to daily for consistent 3-month calculations
            )

            processed_data[symbol] = processed

            # Get actual data frequency for reporting
            actual_freq = detect_actual_frequency(df[symbol])
            print(f"✓ {symbol}: {props['frequency']} data (detected: {actual_freq}), {len(df)} observations")

        except Exception as e:
            print(f"✗ Error processing {symbol}: {str(e)}")
            continue

    if not processed_data:
        raise ValueError("No data could be fetched from FRED API")

    # Combine all processed series into a single DataFrame
    combined_df = pd.DataFrame(processed_data)

    # Forward fill to handle weekends/holidays for daily data
    # But limit the forward fill to avoid excessive propagation
    combined_df = combined_df.fillna(method='ffill', limit=5)

    # Calculate 3-month (90-day) moving averages
    ma_df = pd.DataFrame(index=combined_df.index)
    for symbol in combined_df.columns:
        ma_df[f'{symbol}_3MA'] = combined_df[symbol].rolling(window=90, min_periods=30).mean()

    # Normalize each indicator
    normalized_df = pd.DataFrame(index=combined_df.index)

    for symbol, props in indicators.items():
        if f'{symbol}_3MA' in ma_df.columns:
            normalized = normalize_with_adaptive_zscore(
                ma_df[f'{symbol}_3MA'],
                lookback_days=lookback_periods * 30,
                inverse=props['inverse']
            )
            normalized_df[f'{symbol}_normalized'] = normalized

    # Calculate composite score (weighted by data availability)
    if len(normalized_df.columns) > 0:
        # Calculate composite with handling for missing values
        normalized_df['composite_score'] = calculate_weighted_composite(normalized_df)

    # Combine original and normalized data
    result_df = pd.concat([combined_df, ma_df, normalized_df], axis=1)

    print(f"\nProcessing complete. Data shape: {result_df.shape}")

    return result_df, normalized_df

def detect_actual_frequency(series: pd.Series) -> str:
    """
    Detect the actual frequency of a time series.

    Parameters:
    -----------
    series : pd.Series
        Input series

    Returns:
    --------
    str
        Detected frequency ('daily', 'weekly', 'monthly', or 'irregular')
    """
    if len(series) < 2:
        return "unknown"

    # Calculate time differences
    time_diffs = pd.Series(series.index[1:]) - pd.Series(series.index[:-1])
    median_diff = time_diffs.median()

    if median_diff <= pd.Timedelta(days=2):
        return "daily"
    elif median_diff <= pd.Timedelta(days=10):
        return "weekly"
    elif median_diff <= pd.Timedelta(days=35):
        return "monthly"
    else:
        return "irregular"


def process_series_by_frequency(
    series: pd.Series,
    symbol: str,
    props: dict,
    target_freq: str = 'D'
) -> pd.Series:
    """
    Process series based on its frequency and characteristics.

    Parameters:
    -----------
    series : pd.Series
        Input series
    symbol : str
        Symbol name
    props : dict
        Properties including frequency and transformation
    target_freq : str
        Target frequency for resampling ('D' for daily)

    Returns:
    --------
    pd.Series
        Processed series at target frequency
    """
    # Handle transformations for level series
    if props.get('transform') == 'pct_change':
        # For employment levels, use month-over-month percent change
        if props['frequency'] == 'monthly':
            series = series.pct_change() * 100  # Convert to percentage
        else:
            series = series.pct_change(periods=21) * 100  # 21 trading days ≈ 1 month

    # Resample to target frequency based on original frequency
    if props['frequency'] == 'daily':
        # Already daily, just ensure proper index
        processed = series.resample('D').last().fillna(method='ffill', limit=5)

    elif props['frequency'] == 'weekly':
        # Weekly data - interpolate to daily with limits
        # Use linear interpolation but limit to avoid over-extrapolation
        processed = series.resample('D').interpolate(method='linear', limit=6)

    elif props['frequency'] == 'monthly':
        # Monthly data - use more conservative interpolation
        # First forward fill, then interpolate for smoother transitions
        resampled = series.resample('D').fillna(method='ffill')
        # Apply smooth interpolation between monthly points
        processed = resampled.interpolate(method='cubic', limit_direction='forward')

    else:
        # Unknown frequency, attempt to detect and process
        processed = auto_resample_to_daily(series)

    return processed


def auto_resample_to_daily(series: pd.Series) -> pd.Series:
    """
    Automatically detect frequency and resample to daily.

    Parameters:
    -----------
    series : pd.Series
        Input series with unknown frequency

    Returns:
    --------
    pd.Series
        Daily resampled series
    """
    freq = detect_actual_frequency(series)

    if freq == "daily":
        return series.resample('D').last().fillna(method='ffill', limit=5)
    elif freq == "weekly":
        return series.resample('D').interpolate(method='linear', limit=6)
    elif freq == "monthly":
        resampled = series.resample('D').fillna(method='ffill')
        return resampled.interpolate(method='cubic', limit_direction='forward')
    else:
        # For irregular data, use simple forward fill
        return series.resample('D').fillna(method='ffill')


def calculate_weighted_composite(normalized_df: pd.DataFrame) -> pd.Series:
    """
    Calculate weighted composite score giving more weight to indicators with more recent data.

    Parameters:
    -----------
    normalized_df : pd.DataFrame
        DataFrame with normalized indicators

    Returns:
    --------
    pd.Series
        Weighted composite score
    """
    # Get only normalized columns (exclude composite_score if it exists)
    norm_cols = [col for col in normalized_df.columns if '_normalized' in col]

    if not norm_cols:
        return pd.Series(index=normalized_df.index)

    # Calculate weights based on data recency and availability
    weights = {}
    for col in norm_cols:
        # Check how recent the data is
        last_valid = normalized_df[col].last_valid_index()
        if last_valid is not None:
            days_old = (normalized_df.index[-1] - last_valid).days
            # Weight decreases with age of data
            weight = max(0.1, 1.0 - (days_old / 30))  # Reduce weight by ~3% per day old
            weights[col] = weight
        else:
            weights[col] = 0

    # Normalize weights
    total_weight = sum(weights.values())
    if total_weight > 0:
        weights = {k: v/total_weight for k, v in weights.items()}

    # Calculate weighted average
    composite = pd.Series(index=normalized_df.index, dtype=float)
    for idx in normalized_df.index:
        weighted_sum = 0
        weight_sum = 0
        for col in norm_cols:
            if pd.notna(normalized_df.loc[idx, col]):
                weighted_sum += normalized_df.loc[idx, col] * weights.get(col, 0)
                weight_sum += weights.get(col, 0)

        if weight_sum > 0:
            composite[idx] = weighted_sum / weight_sum
        else:
            composite[idx] = np.nan

    return composite


def normalize_with_adaptive_zscore(
    series: pd.Series,
    lookback_days: int = 720,  # 24 months
    inverse: bool = False,
    z_bound: float = 2.5
) -> pd.Series:
    """
    Normalize series to -1 to 1 using adaptive z-score with rolling window.

    Parameters:
    -----------
    series : pd.Series
        Input series to normalize (already as 3-month average)
    lookback_days : int
        Number of days for rolling statistics
    inverse : bool
        If True, multiply by -1 (for indicators where higher is worse)
    z_bound : float
        Z-score bounds for clipping (default: 2.5)

    Returns:
    --------
    pd.Series
        Normalized series between -1 and 1
    """
    # Skip if series is all NaN
    if series.isna().all():
        return series

    # Calculate rolling statistics with expanding window for initial period
    min_periods = min(90, len(series.dropna()) // 4)  # At least 90 days or 1/4 of data

    rolling_mean = series.rolling(
        window=lookback_days,
        min_periods=min_periods
    ).mean()

    rolling_std = series.rolling(
        window=lookback_days,
        min_periods=min_periods
    ).std()

    # Use expanding window for initial period
    expanding_mean = series.expanding(min_periods=min_periods).mean()
    expanding_std = series.expanding(min_periods=min_periods).std()

    # Combine rolling and expanding (use expanding for initial period)
    rolling_mean = rolling_mean.fillna(expanding_mean)
    rolling_std = rolling_std.fillna(expanding_std)

    # Avoid division by zero
    rolling_std = rolling_std.replace(0, np.nan)

    # Calculate z-score
    z_score = (series - rolling_mean) / rolling_std

    # Clip z-scores to bounds
    z_score = np.clip(z_score, -z_bound, z_bound)

    # Scale to -1 to 1
    normalized = z_score / z_bound

    # Inverse if needed (for negative indicators)
    if inverse:
        normalized = -normalized

    return normalized


def get_latest_signals(
    df: pd.DataFrame,
    lookback_days: int = 30
) -> Dict[str, any]:
    """
    Extract the latest normalized signals and trends.

    Parameters:
    -----------
    df : pd.DataFrame
        DataFrame with normalized indicators
    lookback_days : int
        Days to look back for trend calculation

    Returns:
    --------
    dict
        Dictionary with latest signals and trends
    """
    signals = {
        'date': None,
        'values': {},
        'trends': {},
        'strength': {},
        'data_age': {}  # How old is the last data point for each indicator
    }

    # Get the last valid row
    valid_data = df.dropna(how='all')
    if len(valid_data) == 0:
        return signals

    latest_idx = valid_data.index[-1]
    signals['date'] = latest_idx.strftime('%Y-%m-%d')

    # Extract normalized columns
    normalized_cols = [col for col in df.columns if '_normalized' in col or col == 'composite_score']

    for col in normalized_cols:
        # Find last valid value for this indicator
        last_valid_idx = df[col].last_valid_index()

        if last_valid_idx is not None:
            # Current value
            current_value = df.loc[last_valid_idx, col]
            signals['values'][col] = round(current_value, 3)

            # Data age
            days_old = (latest_idx - last_valid_idx).days
            signals['data_age'][col] = days_old

            # Calculate trend (change over lookback period)
            past_idx = last_valid_idx - pd.Timedelta(days=lookback_days)
            if past_idx in df.index and pd.notna(df.loc[past_idx, col]):
                past_value = df.loc[past_idx, col]
                trend = current_value - past_value
                signals['trends'][col] = round(trend, 3)

            # Determine signal strength
            abs_value = abs(current_value)
            if abs_value > 0.75:
                strength = "Very Strong"
            elif abs_value > 0.5:
                strength = "Strong"
            elif abs_value > 0.25:
                strength = "Moderate"
            else:
                strength = "Weak"

            signals['strength'][col] = strength

    return signals


def print_summary_table(signals: Dict[str, any]):
    """
    Print a formatted summary table of the latest signals.

    Parameters:
    -----------
    signals : dict
        Dictionary with signals from get_latest_signals
    """
    print("\n" + "=" * 80)
    print(f"Economic Indicators Dashboard (as of {signals['date']})")
    print("=" * 80)
    print("\nScale: -1.000 (Most Negative) to +1.000 (Most Positive)")
    print("-" * 80)
    print(f"{'Indicator':<25} {'Value':>8} {'Trend':>8} {'Strength':<15} {'Age (days)':<10}")
    print("-" * 80)

    # Sort indicators by absolute value (most significant first)
    sorted_indicators = sorted(signals['values'].items(),
                              key=lambda x: abs(x[1]),
                              reverse=True)

    for indicator, value in sorted_indicators:
        # Format indicator name
        name = indicator.replace('_normalized', '').replace('_', ' ').upper()
        if name == 'COMPOSITE SCORE':
            print("-" * 80)  # Separator before composite

        # Get trend
        trend = signals['trends'].get(indicator, 0)
        trend_str = f"{trend:+.3f}" if trend != 0 else "  -   "

        # Get strength
        strength = signals['strength'].get(indicator, "Unknown")

        # Get data age
        age = signals['data_age'].get(indicator, 0)
        age_str = str(age) if age > 0 else "Current"

        # Determine visual indicator
        if value > 0.5:
            emoji = "🟢"  # Strong positive
        elif value > 0:
            emoji = "🟡"  # Weak positive
        elif value > -0.5:
            emoji = "🟠"  # Weak negative
        else:
            emoji = "🔴"  # Strong negative

        print(f"{emoji} {name:<23} {value:>+8.3f} {trend_str:>8} {strength:<15} {age_str:<10}")

    print("=" * 80)

# 5. Logging Setup
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


# --- SPERANDEO TECHNICAL ANALYSIS FUNCTIONS ---

def get_spy_market_data(ticker: str = "SPY", days: int = 126) -> pd.DataFrame:
    """
    Fetches the last N trading days of OHLCV market data for SPY.
    Enhanced version for Sperandeo analysis requiring full OHLCV data.
    """
    logging.info(f"Fetching last {days} trading days of OHLCV data for {ticker}...")
    end_date = datetime.now()
    start_date = end_date - timedelta(days=int(days * 2.2))
    data = yf.download(
        ticker,
        start=start_date.strftime("%Y-%m-%d"),
        end=end_date.strftime("%Y-%m-%d"),
        progress=False,
        auto_adjust=False,
        ignore_tz=True,
        interval="1d",
    )
    if data.empty:
        raise ValueError(f"Failed to fetch OHLCV data for {ticker}")

    # Handle MultiIndex columns if present
    if isinstance(data.columns, pd.MultiIndex):
        data.columns = data.columns.droplevel(1)

    data = data.dropna()
    data = data.tail(days).copy()
    logging.info(f"OHLCV data fetched: {len(data)} rows.")
    return data


def _local_extrema_flags(df: pd.DataFrame) -> Tuple[pd.Series, pd.Series]:
    """
    Basic local maxima/minima on a one-bar neighborhood.
    """
    is_peak = (df["High"] > df["High"].shift(1)) & (df["High"] > df["High"].shift(-1))
    is_trough = (df["Low"] < df["Low"].shift(1)) & (df["Low"] < df["Low"].shift(-1))
    return is_peak.fillna(False), is_trough.fillna(False)


def identify_trend(df: pd.DataFrame) -> pd.DataFrame:
    """
    Identifies trend regime using swing structure:
    - Uptrend: higher highs and higher lows
    - Downtrend: lower highs and lower lows
    - Else: sideways (0)
    """
    df = df.copy()
    is_peak, is_trough = _local_extrema_flags(df)

    peaks = df["High"].where(is_peak)
    troughs = df["Low"].where(is_trough)

    last_peak = peaks.ffill()
    prev_peak = peaks.ffill().shift(1)
    last_trough = troughs.ffill()
    prev_trough = troughs.ffill().shift(1)

    is_uptrend = (last_peak > prev_peak) & (last_trough > prev_trough)
    is_downtrend = (last_peak < prev_peak) & (last_trough < prev_trough)

    df["trend_state"] = 0
    df.loc[is_uptrend, "trend_state"] = 1
    df.loc[is_downtrend, "trend_state"] = -1
    return df


def quantify_consecutive_days(df: pd.DataFrame) -> pd.DataFrame:
    """
    Computes signed streak length of consecutive up/down closes.
    Positive values: up-day streak length; Negative values: down-day streak length.
    """
    df = df.copy()
    price_change = df["Close"].diff()
    sign = np.sign(price_change).fillna(0)
    grp = (sign != sign.shift()).cumsum()
    counts = sign.groupby(grp).cumcount() + 1
    df["consecutive_days"] = counts * sign
    return df


def detect_trendline_break(df: pd.DataFrame) -> pd.DataFrame:
    """
    Approximates trendline breaks using the last two swing lows (uptrend) or highs (downtrend).
    - For uptrend line: connect last two swing lows; if Close crosses below the extrapolated line -> bearish break (+1).
    - For downtrend line: connect last two swing highs; if Close crosses above the extrapolated line -> bullish break (-1).

    Output:
      trendline_break:
        +1: bearish break of uptrend line
        -1: bullish break of downtrend line
         0: none
      trendline_slope_up, trendline_slope_down: slopes for context (NaN if not defined)
    """
    df = df.copy()
    is_peak, is_trough = _local_extrema_flags(df)

    n = len(df)
    tl_break = np.zeros(n, dtype=int)
    slope_up = np.full(n, np.nan)
    slope_down = np.full(n, np.nan)

    lows: List[Tuple[int, float]] = []
    highs: List[Tuple[int, float]] = []

    last_up_cross = False
    last_down_cross = False

    closes = df["Close"].values
    lows_arr = df["Low"].values
    highs_arr = df["High"].values

    for i in range(n):
        if is_trough.iloc[i]:
            lows.append((i, lows_arr[i]))
            if len(lows) > 2:
                lows.pop(0)
        if is_peak.iloc[i]:
            highs.append((i, highs_arr[i]))
            if len(highs) > 2:
                highs.pop(0)

        # Uptrend line from two recent swing lows
        if len(lows) == 2:
            (x1, y1), (x2, y2) = lows[0], lows[1]
            if x2 != x1:
                m_up = (y2 - y1) / (x2 - x1)
                slope_up[i] = m_up
                y_line = y1 + m_up * (i - x1)
                crossed = closes[i] < y_line
                if crossed and not last_up_cross and i >= x2:
                    tl_break[i] = 1  # bearish break
                last_up_cross = crossed

        # Downtrend line from two recent swing highs
        if len(highs) == 2:
            (x1h, y1h), (x2h, y2h) = highs[0], highs[1]
            if x2h != x1h:
                m_dn = (y2h - y1h) / (x2h - x1h)
                slope_down[i] = m_dn
                y_line_dn = y1h + m_dn * (i - x1h)
                crossed_dn = closes[i] > y_line_dn
                if crossed_dn and not last_down_cross and i >= x2h:
                    tl_break[i] = -1  # bullish break
                last_down_cross = crossed_dn

    df["trendline_break"] = tl_break
    df["trendline_slope_up"] = slope_up
    df["trendline_slope_down"] = slope_down
    return df


def apply_123_rule(df: pd.DataFrame, use_trendline_condition: bool = True) -> pd.DataFrame:
    """
    Applies Sperandeo's 1-2-3 Rule, tracking "armed" and "triggered" states.

    1-2-3 Top:
      1) Break of uptrend line (if use_trendline_condition=True, require trendline_break=+1)
      2) Lower high forms
      3) Price breaks below last swing low -> trigger

    1-2-3 Bottom: symmetric.

    Output:
      123_reversal_state:
        +1.0: Top Triggered
        +0.5: Top Armed (lower high formed after condition 1)
        -1.0: Bottom Triggered
        -0.5: Bottom Armed (higher low formed after condition 1)
    """
    df = df.copy()
    is_peak, is_trough = _local_extrema_flags(df)

    peak_vals = df["High"].where(is_peak)
    trough_vals = df["Low"].where(is_trough)

    last_peak = peak_vals.ffill()
    last_trough = trough_vals.ffill()

    # 2) Lower high formed? (vs previous peak)
    prev_peak_at_peaks = peak_vals.shift(1)
    lower_high_now = is_peak & (df["High"] < prev_peak_at_peaks)

    # 2) Higher low formed? (vs previous trough)
    prev_trough_at_troughs = trough_vals.shift(1)
    higher_low_now = is_trough & (df["Low"] > prev_trough_at_troughs)

    # Optional: enforce Condition 1 via trendline breaks (from detect_trendline_break)
    if use_trendline_condition and "trendline_break" in df.columns:
        cond1_top = df["trendline_break"].fillna(0).astype(int) == 1     # bearish break of uptrend line
        cond1_bot = df["trendline_break"].fillna(0).astype(int) == -1    # bullish break of downtrend line
    else:
        # If not using trendline condition, allow arms to form without it
        cond1_top = pd.Series(True, index=df.index)
        cond1_bot = pd.Series(True, index=df.index)

    # Persist "armed" state forward after Condition 1 occurs
    df["top_armed"] = (cond1_top & lower_high_now).replace(False, np.nan).ffill().fillna(False).infer_objects(copy=False)
    df["bottom_armed"] = (cond1_bot & higher_low_now).replace(False, np.nan).ffill().fillna(False).infer_objects(copy=False)

    # 3) Trigger when price breaches last swing low (top) or last swing high (bottom)
    break_below_last_trough = df["Close"] < last_trough
    break_above_last_peak = df["Close"] > last_peak

    top_triggered = df["top_armed"] & break_below_last_trough
    bottom_triggered = df["bottom_armed"] & break_above_last_peak

    state = np.zeros(len(df))
    state[df["top_armed"]] = 0.5
    state[df["bottom_armed"]] = -0.5
    state[top_triggered] = 1.0
    state[bottom_triggered] = -1.0

    df["123_reversal_state"] = state

    # Clean up helper columns
    df.drop(columns=["top_armed", "bottom_armed"], inplace=True)
    return df


def apply_2b_rule(df: pd.DataFrame, lookback: int = 5) -> pd.DataFrame:
    """
    Applies Sperandeo's 2B rule (failed breakout/breakdown) with a configurable lookback.
    Adds 2B amplitude (strength) based on overshoot relative to the reference swing level.

    Output:
      2b_signal:
        +1: 2B Top (failed breakout) -> Bearish
        -1: 2B Bottom (failed breakdown) -> Bullish
         0: none
      2b_strength: overshoot/undershoot magnitude (0..)
    """
    df = df.copy()
    is_peak, is_trough = _local_extrema_flags(df)

    last_peak = df["High"].where(is_peak).ffill()
    last_trough = df["Low"].where(is_trough).ffill()

    # Breakout above last peak in recent window, then close back below that peak level -> 2B Top
    breakout = df["High"] > last_peak.shift(1)
    breakout_recent = breakout.rolling(window=lookback, min_periods=1).max().astype(bool)
    failed_breakout = df["Close"] < last_peak.shift(1)
    top_2b = breakout_recent & failed_breakout

    # Breakdown below last trough in recent window, then close back above that trough -> 2B Bottom
    breakdown = df["Low"] < last_trough.shift(1)
    breakdown_recent = breakdown.rolling(window=lookback, min_periods=1).max().astype(bool)
    failed_breakdown = df["Close"] > last_trough.shift(1)
    bottom_2b = breakdown_recent & failed_breakdown

    # De-duplicate contiguous True spans (flag only first bar of a new event)
    top_trigger = top_2b & (~top_2b.shift(1).fillna(False).infer_objects(copy=False))
    bottom_trigger = bottom_2b & (~bottom_2b.shift(1).fillna(False).infer_objects(copy=False))

    sig = np.zeros(len(df), dtype=int)
    sig[top_trigger] = 1
    sig[bottom_trigger] = -1
    df["2b_signal"] = sig

    # 2B amplitude (strength): max overshoot/undershoot in the lookback window prior to trigger
    strength = np.zeros(len(df))
    for i in np.where(top_trigger)[0]:
        ref = float(last_peak.shift(1).iloc[i]) if not np.isnan(last_peak.shift(1).iloc[i]) else np.nan
        if not np.isnan(ref) and ref != 0:
            lo = max(0, i - lookback + 1)
            overshoot = (df["High"].iloc[lo:i+1].max() - ref) / abs(ref)
            strength[i] = max(0.0, overshoot)
    for i in np.where(bottom_trigger)[0]:
        ref = float(last_trough.shift(1).iloc[i]) if not np.isnan(last_trough.shift(1).iloc[i]) else np.nan
        if not np.isnan(ref) and ref != 0:
            lo = max(0, i - lookback + 1)
            undershoot = (ref - df["Low"].iloc[lo:i+1].min()) / abs(ref)
            strength[i] = max(0.0, undershoot)
    df["2b_strength"] = strength

    return df


def apply_four_day_rule(df: pd.DataFrame) -> pd.DataFrame:
    """
    Implements the spirit of Sperandeo's Four-Day Rule:
      - After 4+ consecutive up days, the first down day is a potential bearish reversal day.
      - After 4+ consecutive down days, the first up day is a potential bullish reversal day.

    Output:
      four_day_signal:
        +1: bearish reversal day after >=4 up days
        -1: bullish reversal day after >=4 down days
         0: none
    """
    df = df.copy()
    df = quantify_consecutive_days(df)
    price_change = df["Close"].diff()
    sign = np.sign(price_change).fillna(0)

    prev_streak = df["consecutive_days"].shift(1).fillna(0)
    current_sign = sign

    bearish_reversal = (prev_streak >= 4) & (current_sign < 0)
    bullish_reversal = (prev_streak <= -4) & (current_sign > 0)

    sig = np.zeros(len(df), dtype=int)
    sig[bearish_reversal] = 1
    sig[bullish_reversal] = -1
    df["four_day_signal"] = sig
    return df


def generate_sperandeo_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Master function to compute all Sperandeo-related features.
    """
    features = df.copy()
    features = identify_trend(features)
    features = detect_trendline_break(features)
    features = apply_123_rule(features, use_trendline_condition=True)
    features = apply_2b_rule(features, lookback=5)
    features = apply_four_day_rule(features)
    return features


def construct_sperandeo_data_context(spy_ohlcv: pd.DataFrame,
                                    sperandeo_features: pd.DataFrame,
                                    market_data: pd.DataFrame) -> str:
    """
    Construct data context for pure Sperandeo technical analysis to be appended to the agent prompt.
    Focuses exclusively on price action and technical indicators per Sperandeo methodology.
    """
    if sperandeo_features.empty:
        return "No technical analysis data available."

    latest = sperandeo_features.iloc[-1]

    # Include 2B strength in prompt
    twob_strength = latest.get("2b_strength", 0.0)

    # Prepare SPY data for analysis (last 50 days for context)
    spy_data_str = spy_ohlcv[['Open', 'High', 'Low', 'Close', 'Volume']].tail(50).round(2).to_string()

    # Market context from existing data (VIX, IRX for technical context only)
    market_str = market_data.tail(10).to_string()

    data_context = f"""
CURRENT MARKET DATA:
Recent SPY Price Action (Last 50 Days):
{spy_data_str}

Current Market Context (VIX, IRX):
{market_str}

PRE-COMPUTED SPERANDEO INDICATORS (Latest Day):
- Trend State: {latest['trend_state']:.1f} (1: Uptrend, -1: Downtrend, 0: Sideways)
- 1-2-3 Reversal: {latest['123_reversal_state']:.1f} (1: Top Triggered, 0.5: Top Armed, -1: Bottom Triggered, -0.5: Bottom Armed)
- 2B Pattern: {latest['2b_signal']:.1f} (1: 2B Top/Bearish, -1: 2B Bottom/Bullish)
- 2B Strength: {twob_strength:.3f} (Magnitude of the false breakout)
- Four-Day Rule: {latest.get('four_day_signal', 0):.1f} (1: Bearish, -1: Bullish)
- Trendline Break: {latest.get('trendline_break', 0):.1f} (+1: Bearish break, -1: Bullish break)
- Consecutive Days: {latest['consecutive_days']:.1f}
"""

    return data_context

# --- STRUCTURED COMMUNICATION PROTOCOL ---

@dataclass
class AnalystReport:
    """Structured report format for analyst agents"""
    analyst_type: str
    ticker: str
    date: str
    key_findings: List[str]
    metrics: Dict[str, Any]
    recommendation: str
    confidence: float
    raw_data: Optional[Dict] = None

@dataclass
class ResearcherOpinion:
    """Structured format for researcher debate contributions"""
    perspective: str  # "bullish" or "bearish"
    key_arguments: List[str]
    supporting_evidence: Dict[str, Any]
    risk_assessment: str
    confidence: float

@dataclass
class TradingDecision:
    """Structured trading decision format"""
    action: str  # "BUY_CALL", "BUY_PUT", "NO_TRADE"
    strategy: str
    dte: int
    confidence: float
    reasoning: str
    risk_assessment: Dict[str, float]

@dataclass
class RiskAssessment:
    """Risk management team assessment"""
    risk_profile: str  # "aggressive", "neutral", "conservative"
    assessment: str
    recommended_adjustments: List[str]
    risk_score: float

# --- AGENT STATE MANAGEMENT ---

class AgentState:
    """Global state management for all agents"""
    def __init__(self):
        self.analyst_reports = {}
        self.researcher_opinions = []
        self.debate_history = []
        self.trading_decision = None
        self.risk_assessments = []
        self.final_decision = None
        # Structured communication protocol store (per TradingAgents)
        self.protocol = {
            "analyst_reports": {},
            "research_debate": None,
            "trader_decision": None,
            "risk_assessments": [],
            "final_decision": None
        }

    def add_analyst_report(self, report: AnalystReport):
        self.analyst_reports[report.analyst_type] = report
        # Mirror into structured protocol as a plain dict
        if hasattr(self, "protocol"):
            self.protocol.setdefault("analyst_reports", {})
            self.protocol["analyst_reports"][report.analyst_type] = asdict(report)

    def add_researcher_opinion(self, opinion: ResearcherOpinion):
        self.researcher_opinions.append(opinion)

    def add_debate_turn(self, speaker: str, message: str):
        self.debate_history.append({"speaker": speaker, "message": message})

    def record_protocol(self, key: str, value: Any):
        """Record an item into the structured communication protocol."""
        if not hasattr(self, "protocol"):
            self.protocol = {}
        self.protocol[key] = value

    def get_analyst_summary(self) -> str:
        """Generate structured summary of all analyst reports"""
        summary = []
        for analyst_type, report in self.analyst_reports.items():
            summary.append(f"\n{analyst_type.upper()} ANALYSIS:")
            summary.append(f"Key Findings: {'; '.join(report.key_findings)}")
            summary.append(f"Recommendation: {report.recommendation}")
            summary.append(f"Confidence: {report.confidence:.2f}")
        return "\n".join(summary)

# --- AGENT SYSTEM PROMPTS (Version 2.0) ---

AGENT_PROMPTS = {
    "technical_analyst": """
You are Victor Sperandeo ("Trader Vic"), the legendary trader and author of "Trader Vic - Methods of a Wall Street Master." Your analysis is disciplined, methodical, and grounded in your specific rules for identifying trend reversals.

Your task is to provide a comprehensive technical analysis of SPY using your signature methodology for the Option Colab trading system.

CONFIDENCE CALIBRATION INSTRUCTIONS:
You are making important financial decisions, thus you should avoid giving wrong analysis with high confidence. Be very cautious and tend to give lower confidence when:
- Sperandeo rules give conflicting signals
- Trendlines are unclear or multiple timeframes show different trends
- Historical precedent is weak or market conditions are unusual

CONFIDENCE SCORING GUIDELINES (0-100 scale):
- High Confidence (80-100): All three Sperandeo rules align, clear trendline break, strong consistent momentum
- Medium Confidence (50-79): Two of three rules confirm, trendline break, generally consistent technical picture
- Low Confidence (20-49): Only one rule confirms, unclear trendline status, mixed momentum signals, conflicting technical indicators
- Very Low Confidence (0-19): Highly conflicting signals, poor data quality, no clear precedent

METHODOLOGY:
At the heart of your approach is disciplined trend change confirmation using these specific technical indicators:

TREND LINE ANALYSIS: You employ objective trend line drawing:
- Uptrend: Line from lowest low to highest minor low preceding the highest high
- Downtrend: Line from highest high to lowest minor high before the lowest low
- Breaking of this trend line is the initial signal of potential trend change

THE 1-2-3 RULE (Your signature three-step reversal framework):
- Step 1: Break of the established trend line
- Step 2: Test of the high/low - price attempts to retest recent high/low but fails
- Step 3: Move beyond previous minor price point
- ALL THREE conditions must be fulfilled to confirm trend reversal

THE 2B PATTERN (Your most heavily weighted criterion):
- Price moves beyond previous high/low but fails to sustain breakout and quickly reverses
- This "false breakout" indicates momentum loss and is your strongest indicator

THE FOUR-DAY RULE (Market climax identification):
- Four consecutive days moving in trend direction, followed by reversal
- Suggests high probability of trend change within intermediate trend

ANALYSIS REQUIREMENTS WITH CONFIDENCE SCORING:
1. **Primary Trend Assessment**: What is the current dominant trend? Has the trendline been broken? (Confidence: 0-100%)
2. **1-2-3 Rule Status**: Is a 1-2-3 reversal pattern forming or confirmed? (Confidence: 0-100%)
3. **2B Pattern Analysis**: Do you see evidence of a 2B top or bottom? This is your most important criterion. (Confidence: 0-100%)
4. **Key Support/Resistance Levels**: Identify critical price levels based on your swing analysis. (Confidence: 0-100%)
5. **Recent Momentum**: Analyze the last 5-10 trading days for momentum shifts. (Confidence: 0-100%)
6. **Overall Market Bias**: Based on your complete analysis, state your market bias: Bullish, Bearish, or Neutral. (Overall Confidence: 0-100%)

UNCERTAINTY ACKNOWLEDGMENT: Explicitly acknowledge any uncertainties, data limitations, conflicting signals, or factors that reduce your confidence in the analysis.

OUTPUT FORMAT:
Return your analysis as a JSON object with this exact structure:
{
    "primary_trend": {
        "direction": "UPTREND|DOWNTREND|SIDEWAYS",
        "trendline_broken": true|false,
        "confidence": 0-100,
        "reasoning": "Detailed explanation"
    },
    "rule_123_status": {
        "status": "NONE|STEP1|STEP2|STEP3_CONFIRMED",
        "confidence": 0-100,
        "reasoning": "Detailed explanation"
    },
    "pattern_2b_analysis": {
        "pattern_detected": "NONE|2B_TOP|2B_BOTTOM",
        "confidence": 0-100,
        "reasoning": "Detailed explanation"
    },
    "support_resistance": {
        "key_support": 0.0,
        "key_resistance": 0.0,
        "confidence": 0-100,
        "reasoning": "Detailed explanation"
    },
    "momentum_analysis": {
        "recent_momentum": "BULLISH|BEARISH|NEUTRAL",
        "confidence": 0-100,
        "reasoning": "Detailed explanation"
    },
    "overall_bias": {
        "bias": "BULLISH|BEARISH|NEUTRAL",
        "confidence": 0-100,
        "reasoning": "Comprehensive analysis summary"
    },
    "uncertainty_factors": ["List key uncertainties affecting analysis"],
    "key_findings": ["List 3-5 most important technical findings"],
    "recommendation": "BUY_CALL|BUY_PUT|HOLD|AVOID"
}

Provide your analysis in your characteristic disciplined, methodical style. Be specific about what you see in the price action and why it matters according to your proven methodology. Express confidence levels for each key finding and acknowledge uncertainty where appropriate.
""",
    "economist_sentiment_analyst": """
You are a Macroeconomist and Sentiment Specialist. Your task is to analyze the provided economic (FRED) and volatility (VIX) data to paint a picture of the overall market mood and economic health.

CONFIDENCE CALIBRATION INSTRUCTIONS:
You are making important financial decisions, thus you should avoid giving wrong analysis with high confidence. Be very cautious and tend to give lower confidence when:
- FRED indicators give mixed or conflicting signals
- Economic data conflicts with market sentiment (VIX)
- Policy changes or external events could invalidate historical correlations
- Data quality is poor or sample sizes are insufficient

CONFIDENCE SCORING GUIDELINES (0-100 scale):
- High Confidence (80-100): All FRED indicators align, strong historical correlation with market moves, VIX and economic data consistent, clear economic trend
- Medium Confidence (50-79): Most FRED indicators align, moderate historical correlation, some divergence between VIX and economic data, generally consistent picture
- Low Confidence (20-49): FRED indicators give mixed signals, weak historical correlation, significant divergence between indicators, unclear economic trend
- Very Low Confidence (0-19): Highly conflicting economic signals, no historical precedent, overwhelming contradictory evidence

ANALYSIS REQUIREMENTS WITH CONFIDENCE SCORING:
1.  **Economic Outlook:** Based on the Treasury Yield Spread, Economic Policy Uncertainty, Employment data (Payrolls, Initial Claims), Consumer Sentiment, and Federal Funds Rate, what is the current state of the economy? Are conditions expanding, contracting, or showing mixed signals? (Confidence: 0-100%)
2.  **Market Sentiment:** Interpret the VIX level and its recent trend alongside the Equity Market Volatility Tracker. Is the market fearful or complacent? Are volatility measures rising or falling? (Confidence: 0-100%)
3.  **Monetary Policy:** Analyze the Federal Funds Effective Rate - is monetary policy accommodative or restrictive? How does the current rate level and trend affect economic conditions and market sentiment? (Confidence: 0-100%)
4.  **Policy and Uncertainty:** Analyze the Economic Policy Uncertainty Index - how is policy uncertainty affecting market sentiment and economic conditions? (Confidence: 0-100%)
5.  **Employment and Consumer Health:** Examine payroll employment, initial claims, and consumer sentiment - what do these indicate about economic momentum? (Confidence: 0-100%)
6.  **Synthesis:** Combine all economic and sentiment data. Is the market sentiment justified by the economic reality? Are there any divergences between different indicators? (Overall Confidence: 0-100%)

UNCERTAINTY ACKNOWLEDGMENT: Explicitly acknowledge any uncertainties, data limitations, conflicting economic signals, or factors that reduce your confidence in the analysis.

Provide your output as a single, coherent report that synthesizes all available economic and market sentiment indicators. Express confidence levels for each key finding and acknowledge uncertainty where appropriate.
""",
    "option_pricing_analyst": f"""
You are a quantitative analyst specializing in options volatility. Your task is to assess whether buying long SPY options is favorable based on the relationship between implied volatility (VIX) and historical volatility (HV). You must adhere to the fund's research findings.

**Core Logic:**
1.  **Compare VIX to HV:** The primary indicator is the spread between VIX and the {HV_WINDOW}-day HV.
2.  **Assess Favorability:**
    - If VIX is more than 10% above HV (e.g., VIX=20, HV=18), options are considered 'overpriced'. The premium drag makes buying them **unfavorable**.
    - If VIX is below HV, options are 'underpriced' and buying is **favorable**.
    - The default bias is **unfavorable** due to the consistent underperformance of long options found in academic research. You need a compelling reason (like VIX < HV) to call it favorable.
3.  **Directional Consideration:** A very high VIX might be acceptable for a bearish long put strategy if a crash is anticipated, but it is highly unfavorable for a long call strategy. Mention this nuance.
4.  **Carry Cost:** Note the impact of the risk-free rate (^IRX). A higher rate increases the carry cost for long option positions, making it an additional headwind for buyers.
5.  **Expiry Suggestion:**
    - If VIX is high or has spiked recently, suggest **'near-term'** expiries (<= 43 DTE) to capture the immediate move.
    - If VIX is low or compressed, suggest **'long-term'** expiries (> 43 DTE) for a better value proposition.

Your output MUST be a single JSON object with the following structure:
{{
    "vol_assessment": "'favorable' or 'unfavorable'",
    "calibrated_confidence": "An integer from 0 (no confidence in assessment) to 100 (absolute confidence)",
    "reasoning": "A detailed explanation covering your VIX vs. HV comparison, the impact of the risk-free rate, and the default unfavorable bias.",
    "suggested_expiry_type": "'near-term' or 'long-term'"
}}
""",
    "bullish_strategist": """
You are an aggressive and optimistic Bullish Strategist. Using the provided reports from the Technical, Economic, and Options analysts, build the strongest possible case for a positive (upward) move in the market.

-   Focus on any data point that supports a bullish outlook (e.g., price at support, positive economic data, favorable volatility).
-   Downplay or re-interpret any negative data.
-   Conclude with a clear "Bullish Case" summary and a confidence score for your argument.

Your output must be a text report ending with the line:
CONFIDENCE: [a number from 1 to 10]
""",
    "bearish_strategist": """
You are a cautious and pessimistic Bearish Strategist. Using the provided reports from the Technical, Economic, and Options analysts, build the strongest possible case for a negative (downward) move in the market.

-   Focus on any data point that supports a bearish outlook (e.g., price at resistance, negative economic data, unfavorable volatility).
-   Downplay or re-interpret any positive data.
-   Conclude with a clear "Bearish Case" summary and a confidence score for your argument.

Your output must be a text report ending with the line:
CONFIDENCE: [a number from 1 to 10]
""",
    "chief_trader": f"""
You are the Chief Trader. You have received reports from your Technical, Economic, and Option Pricing analysts, the conflicting arguments from your strategists, and information on your current portfolio.

ANALYTICAL WEIGHTING FRAMEWORK:
Your decision-making process must prioritize inputs according to this specific weighting scheme:
- **Technical Analysis: 50%** - The Technical Analyst's Sperandeo-based analysis is your PRIMARY input and should drive half of your decision weight
- **Economic Analysis: 20%** - FRED indicators and economic sentiment analysis
- **Option Pricing Analysis: 15%** - VIX vs Historical Volatility assessment and favorability
- **Sentiment/News Analysis: 10%** - AlphaVantage news and sentiment data
- **Researcher Debate: 5%** - Bull/Bear strategist debate outcome

CONFIDENCE AGGREGATION AND RISK MANAGEMENT:
You are making important financial decisions with real capital at risk, thus you should avoid making high-risk trades with high confidence when analyst confidence levels are low or conflicting. Be very cautious when:
- Technical Analyst has low confidence (<50%) - this is CRITICAL given the 50% weighting
- Multiple analysts have low confidence scores (<50%)
- Analysts give conflicting recommendations
- Market conditions are uncertain or unprecedented
- Option pricing is unfavorable according to volatility analysis

CONFIDENCE AGGREGATION METHODOLOGY:
1. **Technical Analysis Priority**: Give primary weight (50%) to Technical Analyst's confidence and recommendation
2. **Weighted Analyst Assessment**: Review each analyst's confidence level weighted by their importance in the framework
3. **Consensus Analysis**: Determine if analysts agree or conflict, with special attention to technical analysis alignment
4. **Risk-Adjusted Confidence**: Lower your overall confidence when facing uncertainty, conflicting signals, or unfavorable risk/reward
5. **Cautious Steering**: When Technical Analyst confidence is low (<60%) or conflicts with other high-weight analysts, strongly consider "No Trade"

Your task is to synthesize all this information and make a single, decisive, and actionable trading decision for SPY options for the next trading day. You must weigh all evidence according to the specified framework, with Technical Analysis as your primary driver.

Your final output MUST be a single JSON object with the following structure:
{{
    "decision": "BULLISH", "BEARISH", or "NEUTRAL",
    "strategy": "A specific options strategy (e.g., 'Buy Call', 'Buy Put', 'Bull Call Spread', 'Bear Put Spread', 'Iron Condor', 'No Trade')",
    "dte": An integer representing the chosen Days to Expiration from this list: {DTE_CHOICES},
    "confidence": An integer from 1 (very low) to 10 (very high) in your overall decision,
    "analyst_confidence_summary": "Brief summary of each analyst's confidence level and how it influenced your decision",
    "uncertainty_factors": "List any significant uncertainties or conflicting signals that reduce confidence",
    "risk_assessment": "Assessment of potential downside if the trade goes wrong",
    "reasoning": "A detailed paragraph explaining how you synthesized ALL inputs according to the weighting framework (Technical Analysis 50%, Economic 20%, Option Pricing 15%, Sentiment/News 10%, Debate 5%) to arrive at your final decision. Explicitly state how the Technical Analyst's recommendation influenced your decision given its 50% weight."
}}
"""
}

# --- STEERINGCONF MULTI-PROMPT FRAMEWORK ---

STEERINGCONF_CHIEF_TRADER_PROMPTS = {
    "very_cautious": f"""
You are the Chief Trader making critical financial decisions with real capital at risk. You have received reports from your Technical, Economic, and Option Pricing analysts, the conflicting arguments from your strategists, and information on your current portfolio.

EXTREME CAUTION REQUIRED: You are making important financial decisions with significant capital at risk, thus you should avoid making high-risk trades with high confidence when there is ANY uncertainty. Be VERY CAUTIOUS and tend to give extremely low confidence on almost all trading decisions unless you have overwhelming evidence from multiple sources.

RISK-FIRST MINDSET:
- Default to "NO_TRADE" unless you have exceptional conviction
- Heavily penalize any decision when analyst confidence levels are low (<70%)
- Require unanimous analyst agreement for high-confidence trades
- Consider worst-case scenarios and potential for significant losses
- When in doubt, preserve capital rather than chase returns

Your task is to synthesize all information with extreme caution and make a trading decision that prioritizes capital preservation above all else.

Your final output MUST be a single JSON object with the following structure:
{{
    "decision": "BULLISH", "BEARISH", or "NEUTRAL",
    "strategy": "A specific options strategy (e.g., 'Buy Call', 'Buy Put', 'Bull Call Spread', 'Bear Put Spread', 'Iron Condor', 'No Trade')",
    "dte": An integer representing the chosen Days to Expiration from this list: {DTE_CHOICES},
    "confidence": An integer from 1 (very low) to 10 (very high) in your overall decision,
    "analyst_confidence_summary": "Brief summary of each analyst's confidence level and how it influenced your decision",
    "uncertainty_factors": "List any significant uncertainties or conflicting signals that reduce confidence",
    "risk_assessment": "Assessment of potential downside if the trade goes wrong",
    "reasoning": "A detailed paragraph explaining your extremely cautious approach and why you chose this decision despite uncertainties."
}}
""",
    "cautious": f"""
You are the Chief Trader making important financial decisions. You have received reports from your Technical, Economic, and Option Pricing analysts, the conflicting arguments from your strategists, and information on your current portfolio.

CAUTIOUS APPROACH: You are making important financial decisions, thus you should avoid making risky trades with high confidence when analyst signals are mixed or uncertain. Be cautious and tend to give lower confidence when facing uncertainty or conflicting analyst recommendations.

RISK-AWARE DECISION MAKING:
- Prefer "NO_TRADE" when analyst confidence is generally low (<60%)
- Require strong analyst consensus for directional trades
- Consider downside risks carefully in your decision process
- Lower your confidence when facing conflicting signals or market uncertainty

Your task is to synthesize all information with appropriate caution and make a prudent trading decision.

Your final output MUST be a single JSON object with the following structure:
{{
    "decision": "BULLISH", "BEARISH", or "NEUTRAL",
    "strategy": "A specific options strategy (e.g., 'Buy Call', 'Buy Put', 'Bull Call Spread', 'Bear Put Spread', 'Iron Condor', 'No Trade')",
    "dte": An integer representing the chosen Days to Expiration from this list: {DTE_CHOICES},
    "confidence": An integer from 1 (very low) to 10 (very high) in your overall decision,
    "analyst_confidence_summary": "Brief summary of each analyst's confidence level and how it influenced your decision",
    "uncertainty_factors": "List any significant uncertainties or conflicting signals that reduce confidence",
    "risk_assessment": "Assessment of potential downside if the trade goes wrong",
    "reasoning": "A detailed paragraph explaining your cautious approach and decision rationale."
}}
""",
    "vanilla": f"""
You are the Chief Trader. You have received reports from your Technical, Economic, and Option Pricing analysts, the conflicting arguments from your strategists, and information on your current portfolio.

BALANCED APPROACH: Your task is to synthesize all this information and make a single, decisive, and actionable trading decision for SPY options for the next trading day. You must weigh all evidence, including the quantitative assessment of option pricing and analyst confidence levels.

DECISION FRAMEWORK:
- Consider all analyst inputs and their confidence levels
- Balance risk and reward appropriately
- Make decisions based on the weight of evidence
- Acknowledge uncertainties while still making actionable decisions

Your final output MUST be a single JSON object with the following structure:
{{
    "decision": "BULLISH", "BEARISH", or "NEUTRAL",
    "strategy": "A specific options strategy (e.g., 'Buy Call', 'Buy Put', 'Bull Call Spread', 'Bear Put Spread', 'Iron Condor', 'No Trade')",
    "dte": An integer representing the chosen Days to Expiration from this list: {DTE_CHOICES},
    "confidence": An integer from 1 (very low) to 10 (very high) in your overall decision,
    "analyst_confidence_summary": "Brief summary of each analyst's confidence level and how it influenced your decision",
    "uncertainty_factors": "List any significant uncertainties or conflicting signals that reduce confidence",
    "risk_assessment": "Assessment of potential downside if the trade goes wrong",
    "reasoning": "A detailed paragraph explaining how you synthesized ALL inputs to arrive at your final decision."
}}
""",
    "confident": f"""
You are the Chief Trader with a mandate to generate returns. You have received reports from your Technical, Economic, and Option Pricing analysts, the conflicting arguments from your strategists, and information on your current portfolio.

CONFIDENT APPROACH: You are making important financial decisions, thus you should avoid giving correct analysis with low confidence. When the evidence supports a directional view, you should have conviction in your decisions and avoid being overly conservative when opportunities present themselves.

OPPORTUNITY-FOCUSED DECISION MAKING:
- Look for opportunities where analyst consensus supports directional trades
- Don't let minor uncertainties prevent you from capitalizing on good setups
- Have conviction when multiple analysts align, even if individual confidence levels aren't perfect
- Balance prudent risk management with the need to generate returns

Your task is to synthesize all information with appropriate confidence and make decisive trading decisions when the evidence supports action.

Your final output MUST be a single JSON object with the following structure:
{{
    "decision": "BULLISH", "BEARISH", or "NEUTRAL",
    "strategy": "A specific options strategy (Four option:'Buy Call', 'Buy Put', 'Close Holding Position', 'Hold Position')",
    "dte": An integer representing the chosen Days to Expiration from this list: {DTE_CHOICES},
    "confidence": An integer from 1 (very low) to 10 (very high) in your overall decision,
    "analyst_confidence_summary": "Brief summary of each analyst's confidence level and how it influenced your decision",
    "uncertainty_factors": "List any significant uncertainties or conflicting signals that reduce confidence",
    "risk_assessment": "Assessment of potential downside if the trade goes wrong",
    "reasoning": "A detailed paragraph explaining your confident approach and why you believe this decision will be profitable."
}}
""",
    "very_confident": f"""
You are the Chief Trader with a strong mandate to generate alpha and outperform the market. You have received reports from your Technical, Economic, and Option Pricing analysts, the conflicting arguments from your strategists, and information on your current portfolio.

VERY CONFIDENT APPROACH: You are making important financial decisions, thus you should avoid giving correct analysis with low confidence. You should be VERY CONFIDENT and tend to give high confidence on almost all trading decisions when you see opportunities. Don't let minor uncertainties or small conflicting signals prevent you from making bold, profitable trades.

AGGRESSIVE OPPORTUNITY CAPTURE:
- Actively seek directional trades when any reasonable evidence supports them
- Have high conviction even when some analysts have moderate confidence
- Focus on upside potential rather than dwelling on minor risks
- Make decisive trades to capture market opportunities
- Confidence should reflect your conviction in profitable outcomes

Your task is to synthesize all information with high confidence and make bold trading decisions that will generate superior returns.

Your final output MUST be a single JSON object with the following structure:
{{
    "decision": "BULLISH", "BEARISH", or "NEUTRAL",
    "strategy": "A specific options strategy (e.g., 'Buy Call', 'Buy Put', 'Bull Call Spread', 'Bear Put Spread', 'Iron Condor', 'No Trade')",
    "dte": An integer representing the chosen Days to Expiration from this list: {DTE_CHOICES},
    "confidence": An integer from 1 (very low) to 10 (very high) in your overall decision,
    "analyst_confidence_summary": "Brief summary of each analyst's confidence level and how it influenced your decision",
    "uncertainty_factors": "List any significant uncertainties or conflicting signals that reduce confidence",
    "risk_assessment": "Assessment of potential downside if the trade goes wrong",
    "reasoning": "A detailed paragraph explaining your very confident approach and why you have high conviction in this profitable trade."
}}
"""
}

# --- STEERINGCONF AGGREGATION ALGORITHMS ---

class SteeringConfAggregator:
    """
    Implements the SteeringConf aggregation methodology from Zhou et al. (2025)
    "Calibrating LLM Confidence with Semantic Steering: A Multi-Prompt Aggregation Framework"
    """

    @staticmethod
    def calculate_answer_consistency(decisions: List[str]) -> Tuple[str, float]:
        """
        Calculate answer consistency (κ_ans) from the research paper.

        Args:
            decisions: List of decision strings from different steering prompts

        Returns:
            Tuple of (dominant_decision, consistency_score)
        """
        if not decisions:
            return "NEUTRAL", 0.0

        # Count frequency of each decision
        decision_counts = {}
        for decision in decisions:
            decision_counts[decision] = decision_counts.get(decision, 0) + 1

        # Find dominant decision and its frequency
        dominant_decision = max(decision_counts, key=decision_counts.get)
        dominant_count = decision_counts[dominant_decision]

        # Calculate consistency score (frequency of dominant answer)
        consistency_score = dominant_count / len(decisions)

        return dominant_decision, consistency_score

    @staticmethod
    def calculate_confidence_consistency(confidences: List[float]) -> Tuple[float, float, float]:
        """
        Calculate confidence consistency (κ_conf) from the research paper.

        Args:
            confidences: List of confidence scores from different steering prompts

        Returns:
            Tuple of (mean_confidence, std_confidence, consistency_score)
        """
        if not confidences:
            return 0.0, 0.0, 0.0

        import numpy as np

        # Calculate mean and standard deviation
        mean_conf = float(np.mean(confidences))
        std_conf = float(np.std(confidences))

        # Calculate confidence consistency score: κ_conf = 1 / (1 + σ_c/μ_c)
        # This penalizes high variance relative to mean confidence
        if mean_conf > 0:
            consistency_score = 1.0 / (1.0 + (std_conf / mean_conf))
        else:
            consistency_score = 0.0

        return mean_conf, std_conf, consistency_score

    @staticmethod
    def calculate_calibrated_confidence(mean_confidence: float,
                                      answer_consistency: float,
                                      confidence_consistency: float) -> float:
        """
        Calculate the final calibrated confidence from the research paper.

        Formula: c(x) = μ_c · κ_ans · κ_conf

        Args:
            mean_confidence: Average confidence across steering prompts
            answer_consistency: Answer consistency score (κ_ans)
            confidence_consistency: Confidence consistency score (κ_conf)

        Returns:
            Calibrated confidence score
        """
        calibrated_conf = mean_confidence * answer_consistency * confidence_consistency
        return max(0.0, min(10.0, calibrated_conf))  # Clamp to valid range

    @staticmethod
    def select_steered_answer(decisions: List[str],
                            confidences: List[float],
                            calibrated_confidence: float,
                            steering_labels: List[str]) -> Tuple[str, int]:
        """
        Select the steered answer closest to the calibrated confidence.

        Args:
            decisions: List of decisions from steering prompts
            confidences: List of confidence scores from steering prompts
            calibrated_confidence: The calculated calibrated confidence
            steering_labels: Labels for steering prompts (for mapping back)

        Returns:
            Tuple of (selected_decision, selected_index)
        """
        if not decisions or not confidences:
            return "NEUTRAL", 0

        # Find the confidence score closest to calibrated confidence
        min_distance = float('inf')
        selected_index = 0

        for i, conf in enumerate(confidences):
            distance = abs(conf - calibrated_confidence)
            if distance < min_distance:
                min_distance = distance
                selected_index = i

        return decisions[selected_index], selected_index

# --- BASE AGENT CLASSES ---

class LLMAgent:
    """A reusable class to interact with the OpenRouter API."""

    def __init__(self, model: str, system_prompt: str):
        self.model = model
        self.system_prompt = system_prompt
        self.api_key = OPENROUTER_API_KEY
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"

    def generate_response(self, user_prompt: str, is_json: bool = False) -> Optional[str]:
        """Generate response from the LLM with retry logic"""
        retries = 3
        for attempt in range(retries):
            try:
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }

                messages = [
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": user_prompt}
                ]

                data = {
                    "model": self.model,
                    "messages": messages,
                    "temperature": 0.1,
                    "max_tokens": 4000
                }

                if is_json:
                    data["response_format"] = {"type": "json_object"}

                response = requests.post(self.base_url, headers=headers, json=data, timeout=60)
                response.raise_for_status()

                result = response.json()
                content = result['choices'][0]['message']['content']

                if content:
                    logging.info(f"LLM response generated successfully (attempt {attempt + 1})")
                    return content.strip()
                else:
                    logging.warning(f"Empty response from LLM (attempt {attempt + 1})")

            except requests.exceptions.RequestException as e:
                logging.error(f"Request error (attempt {attempt + 1}): {e}")
                if attempt < retries - 1:
                    time.sleep(2 ** attempt)
            except KeyError as e:
                logging.error(f"Response parsing error (attempt {attempt + 1}): {e}")
                if attempt < retries - 1:
                    time.sleep(1)
            except Exception as e:
                logging.error(f"Unexpected error (attempt {attempt + 1}): {e}")
                if attempt < retries - 1:
                    time.sleep(1)

        logging.critical(f"LLM Agent failed after {retries} attempts.")
        return None


class BaseAgent(LLMAgent):
    """Base class for all agents with structured output"""

    def __init__(self, model: str, system_prompt: str, agent_type: str):
        super().__init__(model, system_prompt)
        self.agent_type = agent_type

    def generate_structured_response(self, user_prompt: str,
                                    response_class: type) -> Any:
        """Generate response and parse into structured format"""
        response = self.generate_response(user_prompt, is_json=True)
        if response:
            try:
                data = json.loads(response)
                return response_class(**data)
            except (json.JSONDecodeError, TypeError) as e:
                logging.error(f"Failed to parse structured response: {e}")
                return None
        return None

# --- SPECIALIZED AGENT IMPLEMENTATIONS ---

class TechnicalAnalystAgent(BaseAgent):
    """Enhanced technical analyst with Sperandeo methodology"""

    def __init__(self):
        prompt = AGENT_PROMPTS["technical_analyst"]
        super().__init__(DEEP_THINKING_MODEL, prompt, "technical")

    def analyze(self, data: Dict, state: AgentState) -> AnalystReport:
        """Perform technical analysis and return structured report"""
        sperandeo_features = data.get('sperandeo_features')

        if sperandeo_features is not None:
            context = construct_sperandeo_data_context(
                data['spy_ohlcv'],
                sperandeo_features,
                data['market_data']
            )
        else:
            context = self._format_basic_context(data)

        prompt = f"""Analyze the following market data and provide a structured JSON report following the confidence calibration guidelines in your system prompt:

{context}

Return a JSON object with:
- key_findings: list of 3-5 key technical observations with individual confidence levels
- metrics: dict with important technical indicators
- recommendation: "BULLISH", "BEARISH", or "NEUTRAL"
- confidence: float between 0 and 1 (your overall confidence in the analysis)
- uncertainty_factors: list of any uncertainties or conflicting signals
- confidence_breakdown: dict with confidence levels for each Sperandeo rule analysis
"""

        response = self.generate_response(prompt, is_json=True)

        return AnalystReport(
            analyst_type="technical",
            ticker="SPY",
            date=datetime.now().strftime('%Y-%m-%d'),
            key_findings=response.get('key_findings', []),
            metrics=response.get('metrics', {}),
            recommendation=response.get('recommendation', 'NEUTRAL'),
            confidence=response.get('confidence', 0.5)
        )

class EconomistSentimentAnalystAgent(BaseAgent):
    """Economist analyzing FRED data and market sentiment"""

    def __init__(self):
        prompt = AGENT_PROMPTS["economist_sentiment_analyst"]
        super().__init__(DEEP_THINKING_MODEL, prompt, "economist")

    def analyze(self, data: Dict, state: AgentState) -> AnalystReport:
        """Analyze economic indicators and sentiment"""
        fred_signals = data.get('fred_signals', {})

        context = f"""
FRED Economic Indicators (Normalized -1 to +1):
Date: {fred_signals.get('date', 'N/A')}

Values:
{json.dumps(fred_signals.get('values', {}), indent=2)}

Trends (30-day changes):
{json.dumps(fred_signals.get('trends', {}), indent=2)}

Signal Strength:
{json.dumps(fred_signals.get('strength', {}), indent=2)}

VIX Level: {data['market_data']['VIX'].iloc[-1]:.2f}
Risk-Free Rate: {data['market_data'].get('^IRX', pd.Series([3.5])).iloc[-1]:.2f}%

Provide structured analysis focusing on economic outlook and market sentiment.
"""

        prompt = f"""{context}

Return a JSON object following the confidence calibration guidelines in your system prompt:
- key_findings: list of 3-5 key economic observations with individual confidence levels
- metrics: dict with important economic indicators
- recommendation: "RISK_ON", "RISK_OFF", or "NEUTRAL"
- confidence: float between 0 and 1 (your overall confidence in the analysis)
- uncertainty_factors: list of any uncertainties or conflicting economic signals
- confidence_breakdown: dict with confidence levels for each economic indicator analysis
"""

        response = self.generate_response(prompt, is_json=True)

        return AnalystReport(
            analyst_type="economist",
            ticker="SPY",
            date=datetime.now().strftime('%Y-%m-%d'),
            key_findings=response.get('key_findings', []),
            metrics=response.get('metrics', {}),
            recommendation=response.get('recommendation', 'NEUTRAL'),
            confidence=response.get('confidence', 0.5)
)


class AlphaVantageNewsClient:
    """Lightweight client for Alpha Vantage NEWS_SENTIMENT."""
    BASE_URL = "https://www.alphavantage.co/query"

    def __init__(self, api_key: str):
        self.api_key = api_key

    def fetch_news(self, topics: str, time_from: str = None, time_to: str = None,
                   sort: str = "LATEST", limit: int = 100) -> dict:
        params = {
            "function": "NEWS_SENTIMENT",
            "apikey": self.api_key,
            "sort": sort,
            "limit": limit
        }
        if topics:
            params["topics"] = topics
        if time_from:
            params["time_from"] = time_from
        if time_to:
            params["time_to"] = time_to
        try:
            resp = requests.get(self.BASE_URL, params=params, timeout=20)
            resp.raise_for_status()
            return resp.json()
        except Exception as e:
            logging.error(f"Alpha Vantage NEWS_SENTIMENT error: {e}")
            return {"feed": []}


class AlphaVantageBaseAnalyst(BaseAgent):
    """Base for Alpha Vantagepowered analysts with shared helpers."""
    def __init__(self, api_key: str, role: str, topics: str):
        super().__init__(QUICK_THINKING_MODEL, f"You are a {role} using Alpha Vantage news sentiment.", role)
        self.client = AlphaVantageNewsClient(api_key)
        self.topics = topics

    def _aggregate(self, feed: List[dict]) -> dict:
        scores, labels = [], []
        bull = bear = neutral = 0
        for item in feed or []:
            score = item.get("overall_sentiment_score")
            label = item.get("overall_sentiment_label")
            if isinstance(score, str):
                try:
                    score = float(score)
                except:  # noqa
                    score = None
            if score is not None:
                scores.append(score)
            if label:
                labels.append(label)
                if label.upper() == "POSITIVE":
                    bull += 1
                elif label.upper() == "NEGATIVE":
                    bear += 1
                else:
                    neutral += 1
        avg = float(np.mean(scores)) if scores else 0.0

        # Enhanced confidence calibration based on data quality and volume
        n_articles = len(feed or [])
        confidence_raw = abs(avg) if avg != 0 else 0.0

        # Adjust confidence based on sample size (research-based calibration)
        if n_articles >= 80:
            sample_confidence = 0.9  # High confidence with large sample
        elif n_articles >= 40:
            sample_confidence = 0.7  # Medium confidence
        elif n_articles >= 20:
            sample_confidence = 0.5  # Lower confidence
        else:
            sample_confidence = 0.3  # Very low confidence with small sample

        # Adjust confidence based on sentiment consistency
        total_labeled = bull + bear + neutral
        if total_labeled > 0:
            dominant_sentiment = max(bull, bear, neutral) / total_labeled
            consistency_confidence = dominant_sentiment  # Higher when sentiment is consistent
        else:
            consistency_confidence = 0.3

        # Final calibrated confidence (0-100 scale)
        calibrated_confidence = min(100, max(0, confidence_raw * sample_confidence * consistency_confidence * 100))

        return {
            "avg_overall_score": avg,
            "bull": bull,
            "bear": bear,
            "neutral": neutral,
            "n": n_articles,
            "calibrated_confidence": calibrated_confidence,
            "sample_confidence": sample_confidence,
            "consistency_confidence": consistency_confidence
        }


class AlphaVantageSentimentAnalyst(AlphaVantageBaseAnalyst):
    def __init__(self, api_key: str):
        super().__init__(api_key, role="sentiment analyst", topics="financial_markets,economy_macro")

    def analyze(self, data: Dict, state: AgentState) -> AnalystReport:
        asof = data.get("asof_date", datetime.utcnow())
        time_to = asof.strftime('%Y%m%dT%H%M')
        time_from = (asof - timedelta(days=190)).strftime('%Y%m%dT%H%M')
        payload = self.client.fetch_news(self.topics, time_from=time_from, time_to=time_to, sort="LATEST", limit=1000)
        feed = payload.get("feed", [])
        metrics = self._aggregate(feed)

        # Add SPY price context for correlation analysis
        spy_context = ""
        spy_ohlcv = data.get('spy_ohlcv')
        if spy_ohlcv is not None and not spy_ohlcv.empty:
            recent_close = spy_ohlcv['Close'].tail(5)
            spy_trend = "rising" if recent_close.iloc[-1] > recent_close.iloc[0] else "falling"
            spy_context = f"SPY recent trend: {spy_trend} (${recent_close.iloc[-1]:.2f})"

        # Enhanced key findings with confidence awareness
        key_findings = [
            f"Avg sentiment score: {metrics['avg_overall_score']:.3f} (Confidence: {metrics['calibrated_confidence']:.0f}%)",
            f"Bull/Bear/Neutral counts: {metrics['bull']}/{metrics['bear']}/{metrics['neutral']} (n={metrics['n']})",
            f"Data quality: Sample size confidence: {metrics['sample_confidence']:.1f}, Consistency: {metrics['consistency_confidence']:.1f}"
        ]

        if spy_context:
            key_findings.append(f"Market context: {spy_context}")

        # Enhanced rule-based recommendation with confidence consideration
        rec = "NEUTRAL"
        confidence_threshold = 50  # Only make directional calls with reasonable confidence

        if metrics["calibrated_confidence"] >= confidence_threshold:
            if metrics["avg_overall_score"] >= 0.1 and metrics["bull"] > metrics["bear"]:
                rec = "BULLISH"
            elif metrics["avg_overall_score"] <= -0.1 and metrics["bear"] > metrics["bull"]:
                rec = "BEARISH"

        # Add uncertainty acknowledgment for low confidence scenarios
        if metrics["calibrated_confidence"] < confidence_threshold:
            key_findings.append(f"UNCERTAINTY: Low confidence ({metrics['calibrated_confidence']:.0f}%) due to limited data or mixed signals")

        return AnalystReport(
            analyst_type="sentiment",
            ticker="SPY",
            date=datetime.now().strftime('%Y-%m-%d'),
            key_findings=key_findings,
            metrics=metrics,
            recommendation=rec,
            confidence=metrics["calibrated_confidence"] / 100.0,  # Convert to 0-1 scale for AnalystReport
            raw_data={"sample": feed[:5]}  # keep small sample for context
        )


class AlphaVantageNewsAnalyst(AlphaVantageBaseAnalyst):
    def __init__(self, api_key: str):
        super().__init__(api_key, role="news analyst", topics="economy_monetary,economy_fiscal")

    def analyze(self, data: Dict, state: AgentState) -> AnalystReport:
        asof = data.get("asof_date", datetime.utcnow())
        time_to = asof.strftime('%Y%m%dT%H%M')
        time_from = (asof - timedelta(days=190)).strftime('%Y%m%dT%H%M')
        payload = self.client.fetch_news(self.topics, time_from=time_from, time_to=time_to, sort="RELEVANCE", limit=1000)
        feed = payload.get("feed", [])
        metrics = self._aggregate(feed)

        # Add FRED economic context for news analysis
        fred_context = ""
        fred_signals = data.get('fred_signals', {})
        if fred_signals and fred_signals.get('values'):
            econ_indicators = fred_signals['values']
            fred_context = f"Economic context: Policy uncertainty: {econ_indicators.get('USEPUINDXD', 'N/A')}, Consumer sentiment: {econ_indicators.get('UMCSENT', 'N/A')}"

        # Enhanced key findings with confidence awareness for news impact
        key_findings = [
            f"Avg macro news sentiment: {metrics['avg_overall_score']:.3f} (Confidence: {metrics['calibrated_confidence']:.0f}%)",
            f"Distribution B/B/N: {metrics['bull']}/{metrics['bear']}/{metrics['neutral']} (n={metrics['n']})",
            f"News impact assessment: Sample confidence: {metrics['sample_confidence']:.1f}, Consistency: {metrics['consistency_confidence']:.1f}"
        ]

        # Enhanced rule-based recommendation with confidence consideration for news impact
        rec = "NEUTRAL"
        confidence_threshold = 60  # Higher threshold for news impact due to complexity

        if metrics["calibrated_confidence"] >= confidence_threshold:
            if metrics["avg_overall_score"] >= 0.15 and metrics["bull"] >= (metrics["bear"] + 2):
                rec = "BULLISH"
            elif metrics["avg_overall_score"] <= -0.15 and metrics["bear"] >= (metrics["bull"] + 2):
                rec = "BEARISH"

        # Add uncertainty acknowledgment for news impact assessment
        if metrics["calibrated_confidence"] < confidence_threshold:
            key_findings.append(f"UNCERTAINTY: Low confidence ({metrics['calibrated_confidence']:.0f}%) in news impact due to ambiguous signals or limited relevant news")

        # Additional news-specific uncertainty factors
        if metrics["n"] < 50:
            key_findings.append("CAUTION: Limited news volume may not represent full market impact")

        return AnalystReport(
            analyst_type="news",
            ticker="SPY",
            date=datetime.now().strftime('%Y-%m-%d'),
            key_findings=key_findings,
            metrics=metrics,
            recommendation=rec,
            confidence=metrics["calibrated_confidence"] / 100.0,  # Convert to 0-1 scale for AnalystReport
            raw_data={"sample": feed[:5]}
        )

class DebateFacilitator:
    """Manages structured debates between agents"""

    def __init__(self, model: str = QUICK_THINKING_MODEL):
        self.model = model

    def facilitate_debate(self, bull_agent: BaseAgent, bear_agent: BaseAgent,
                         context: str, raw_data: Dict = None, rounds: int = DEBATE_ROUNDS) -> Dict:
        """Facilitate multi-round debate and determine winner with optional raw data access"""
        debate_history = []

        # Prepare enhanced context with raw data if available
        enhanced_context = context
        if raw_data:
            # Add raw SPY OHLCV data for independent analysis
            spy_ohlcv = raw_data.get('spy_ohlcv')
            if spy_ohlcv is not None and not spy_ohlcv.empty:
                spy_recent = spy_ohlcv[['Open', 'High', 'Low', 'Close', 'Volume']].tail(20).round(2).to_string()
                enhanced_context += f"\n\nRAW SPY DATA (Last 20 Days):\n{spy_recent}"

            # Add FRED economic signals for independent analysis
            fred_signals = raw_data.get('fred_signals', {})
            if fred_signals:
                enhanced_context += f"\n\nFRED ECONOMIC INDICATORS:\n{json.dumps(fred_signals.get('values', {}), indent=2)}"

        for round_num in range(rounds):
            # Bull argument
            bull_prompt = f"""Round {round_num + 1}: Make your bullish case using both analyst summaries and raw data.

Context: {enhanced_context}

Previous debate: {json.dumps(debate_history, indent=2) if debate_history else 'First round'}

Provide compelling bullish arguments in 200 words or less. Use raw data to support your independent analysis."""

            bull_response = bull_agent.generate_response(bull_prompt)
            debate_history.append({"round": round_num + 1, "bull": bull_response})

            # Bear argument
            bear_prompt = f"""Round {round_num + 1}: Make your bearish case and respond to bull arguments using both analyst summaries and raw data.

Context: {enhanced_context}

Bull's argument: {bull_response}

Provide compelling bearish arguments in 200 words or less. Use raw data to support your independent analysis."""

            bear_response = bear_agent.generate_response(bear_prompt)
            debate_history.append({"round": round_num + 1, "bear": bear_response})

        # Determine prevailing perspective
        summary_prompt = f"""Analyze this debate and determine the prevailing perspective:

{json.dumps(debate_history, indent=2)}

Return JSON with:
- prevailing_view: "BULLISH" or "BEARISH"
- confidence: float 0-1
- key_points: list of winning arguments
"""

        facilitator = LLMAgent(self.model, "You are an impartial debate judge.")
        result = facilitator.generate_response(summary_prompt, is_json=True)

        return {
            "debate_history": debate_history,
            "result": result
        }

class RiskManagementTeam:
    """Three-perspective risk management team"""

    def __init__(self):
        self.aggressive = BaseAgent(
            DEEP_THINKING_MODEL,
            "You are an aggressive risk manager focused on maximizing returns.",
            "aggressive"
        )
        self.neutral = BaseAgent(
            DEEP_THINKING_MODEL,
            "You are a balanced risk manager seeking optimal risk-reward.",
            "neutral"
        )
        self.conservative = BaseAgent(
            DEEP_THINKING_MODEL,
            "You are a conservative risk manager focused on capital preservation.",
            "conservative"
        )

    def assess_decision(self, decision: TradingDecision,
                       state: AgentState, raw_data: Dict = None) -> List[RiskAssessment]:
        """Get risk assessments from all three perspectives with optional raw data"""
        assessments = []

        context = f"""
Trading Decision: {json.dumps(asdict(decision), indent=2)}"""

        # Add raw data risk context if available
        if raw_data:
            spy_ohlcv = raw_data.get('spy_ohlcv')
            if spy_ohlcv is not None and not spy_ohlcv.empty:
                # Calculate recent volatility for risk assessment
                returns = spy_ohlcv['Close'].pct_change().tail(20)
                volatility = returns.std() * np.sqrt(252) * 100  # Annualized volatility %
                context += f"\n\nRAW RISK DATA:\n- 20-day annualized volatility: {volatility:.1f}%"

                # Add VIX context if available
                market_data = raw_data.get('market_data', {})
                if '^VIX' in market_data:
                    vix_current = market_data['^VIX'].tail(1).iloc[0] if not market_data['^VIX'].empty else None
                    if vix_current:
                        context += f"\n- Current VIX: {vix_current:.1f}"

        context += f"""

Analyst Reports Summary:
{state.get_analyst_summary()}

Assess this decision from your risk perspective.
"""

        for agent, profile in [(self.aggressive, "aggressive"),
                               (self.neutral, "neutral"),
                               (self.conservative, "conservative")]:

            prompt = f"""{context}

Provide risk assessment as JSON:
- assessment: your detailed assessment
- recommended_adjustments: list of suggested adjustments
- risk_score: float 0-10 (10 = highest risk)
"""

            response = agent.generate_response(prompt, is_json=True)

            assessments.append(RiskAssessment(
                risk_profile=profile,
                assessment=response.get('assessment', ''),
                recommended_adjustments=response.get('recommended_adjustments', []),
                risk_score=response.get('risk_score', 5.0)
            ))

        return assessments

class ChiefTrader(BaseAgent):
    """Enhanced chief trader with structured decision-making"""

    def __init__(self):
        prompt = AGENT_PROMPTS["chief_trader"]
        super().__init__(DEEP_THINKING_MODEL, prompt, "trader")

    def _calculate_black_scholes(self, spot_price: float, strike_price: float,
                                time_to_expiration: float, risk_free_rate: float,
                                volatility: float, option_type: str = 'call') -> float:
        """
        Calculate Black-Scholes option price.

        Args:
            spot_price: Current price of underlying asset
            strike_price: Strike price of option
            time_to_expiration: Time to expiration in years
            risk_free_rate: Risk-free interest rate (as decimal)
            volatility: Volatility (as decimal, not percentage)
            option_type: 'call' or 'put'

        Returns:
            Theoretical option price
        """

        # Handle edge cases
        if time_to_expiration <= 0:
            if option_type.lower() == 'call':
                return max(0, spot_price - strike_price)
            else:
                return max(0, strike_price - spot_price)

        # Calculate d1 and d2
        d1 = (np.log(spot_price / strike_price) +
              (risk_free_rate + 0.5 * volatility**2) * time_to_expiration) / (volatility * np.sqrt(time_to_expiration))
        d2 = d1 - volatility * np.sqrt(time_to_expiration)

        if option_type.lower() == 'call':
            # Call option price
            price = (spot_price * norm.cdf(d1) -
                    strike_price * np.exp(-risk_free_rate * time_to_expiration) * norm.cdf(d2))
        else:
            # Put option price
            price = (strike_price * np.exp(-risk_free_rate * time_to_expiration) * norm.cdf(-d2) -
                    spot_price * norm.cdf(-d1))

        return max(0, price)  # Ensure non-negative price

    def _get_analyst_summary_by_type(self, state: AgentState, analyst_type: str) -> str:
        """Get summary for a specific analyst type"""
        report = state.analyst_reports.get(analyst_type)
        if not report:
            return f"No {analyst_type} analysis available"

        return f"""
RECOMMENDATION: {report.recommendation}
CONFIDENCE: {report.confidence:.2f}
KEY FINDINGS: {'; '.join(report.key_findings[:2])}"""  # Limit to top 2 findings for brevity

    def _analyze_option_pricing(self, raw_data: Dict) -> Dict:
        """
        Analyze option pricing favorability based on VIX vs HV relationship.
        Implements the logic from option_pricing_analyst prompt.

        Args:
            raw_data: Dictionary containing market data including VIX, HV, and risk-free rate

        Returns:
            Dictionary with vol_assessment, confidence, reasoning, and suggested_expiry_type
        """
        try:
            market_data = raw_data.get('market_data', {})

            # Extract VIX and HV data
            vix_series = market_data.get('^VIX')
            hv_series = market_data.get('HV')
            irx_series = market_data.get('^IRX')  # Risk-free rate

            if vix_series is None or hv_series is None:
                return {
                    "vol_assessment": "unfavorable",
                    "calibrated_confidence": 30,
                    "reasoning": "Insufficient data for VIX vs HV analysis. Default to unfavorable bias.",
                    "suggested_expiry_type": "long-term"
                }

            # Get current values
            current_vix = vix_series.iloc[-1] if not vix_series.empty else None
            current_hv = hv_series.iloc[-1] if not hv_series.empty else None
            current_irx = irx_series.iloc[-1] if irx_series is not None and not irx_series.empty else 5.0

            if current_vix is None or current_hv is None or np.isnan(current_vix) or np.isnan(current_hv):
                return {
                    "vol_assessment": "unfavorable",
                    "calibrated_confidence": 30,
                    "reasoning": "Missing or invalid VIX/HV data. Default to unfavorable bias.",
                    "suggested_expiry_type": "long-term"
                }

            # Core logic: Compare VIX to HV
            vix_hv_spread = current_vix - current_hv
            vix_hv_ratio = current_vix / current_hv if current_hv > 0 else float('inf')

            # Assess favorability based on 10% threshold
            if current_vix < current_hv:
                # VIX below HV - options underpriced, favorable
                vol_assessment = "favorable"
                confidence = min(85, 60 + abs(vix_hv_spread) * 2)  # Higher confidence for larger spreads
                reasoning = f"VIX ({current_vix:.1f}) below HV ({current_hv:.1f}) indicates underpriced options. "
            elif vix_hv_ratio > 1.10:  # VIX more than 10% above HV
                # Options overpriced, unfavorable
                vol_assessment = "unfavorable"
                confidence = min(90, 70 + (vix_hv_ratio - 1.10) * 100)
                reasoning = f"VIX ({current_vix:.1f}) is {((vix_hv_ratio - 1) * 100):.1f}% above HV ({current_hv:.1f}), indicating overpriced options. "
            else:
                # Close to fair value, but default bias is unfavorable
                vol_assessment = "unfavorable"
                confidence = 55
                reasoning = f"VIX ({current_vix:.1f}) close to HV ({current_hv:.1f}), but default bias is unfavorable due to academic research on long option underperformance. "

            # Add risk-free rate impact
            if current_irx > 4.0:
                reasoning += f"High risk-free rate ({current_irx:.1f}%) increases carry cost for long positions. "
                if vol_assessment == "favorable":
                    confidence = max(40, confidence - 10)  # Reduce confidence for favorable assessment

            # Suggest expiry type based on VIX level
            if current_vix > 25 or (len(vix_series) >= 5 and current_vix > vix_series.iloc[-5] * 1.2):
                suggested_expiry_type = "near-term"
                reasoning += "High/spiking VIX suggests near-term expiries to capture immediate moves."
            else:
                suggested_expiry_type = "long-term"
                reasoning += "Normal/low VIX suggests long-term expiries for better value."

            return {
                "vol_assessment": vol_assessment,
                "calibrated_confidence": int(confidence),
                "reasoning": reasoning.strip(),
                "suggested_expiry_type": suggested_expiry_type
            }

        except Exception as e:
            logging.error(f"Error in option pricing analysis: {e}")
            return {
                "vol_assessment": "unfavorable",
                "calibrated_confidence": 20,
                "reasoning": f"Error in analysis: {str(e)}. Default to unfavorable bias.",
                "suggested_expiry_type": "long-term"
            }

    def make_decision(self, state: AgentState, raw_data: Dict = None) -> TradingDecision:
        """Synthesize all information and make trading decision with optional raw data validation"""

        # Perform option pricing analysis
        option_analysis = self._analyze_option_pricing(raw_data) if raw_data else {
            "vol_assessment": "unfavorable",
            "calibrated_confidence": 30,
            "reasoning": "No market data available for option pricing analysis",
            "suggested_expiry_type": "long-term"
        }

        # Extract and emphasize technical analysis (50% weight)
        technical_report = state.analyst_reports.get('technical')
        technical_summary = "No technical analysis available"
        if technical_report:
            technical_summary = f"""
RECOMMENDATION: {technical_report.recommendation}
CONFIDENCE: {technical_report.confidence:.2f}
KEY FINDINGS: {'; '.join(technical_report.key_findings)}"""

        # Prepare context with weighted emphasis
        context = f"""
=== PRIMARY ANALYSIS (50% WEIGHT) ===
TECHNICAL ANALYSIS (Sperandeo-based):
{technical_summary}

=== SUPPORTING ANALYSES ===
ECONOMIC ANALYSIS (20% weight):
{self._get_analyst_summary_by_type(state, 'economist')}

OPTION PRICING ANALYSIS (15% weight):
- Assessment: {option_analysis['vol_assessment'].upper()}
- Confidence: {option_analysis['calibrated_confidence']}%
- Reasoning: {option_analysis['reasoning']}
- Suggested Expiry: {option_analysis['suggested_expiry_type']}

SENTIMENT/NEWS ANALYSIS (10% weight):
{self._get_analyst_summary_by_type(state, 'sentiment')}
{self._get_analyst_summary_by_type(state, 'news')}

RESEARCHER DEBATE (5% weight):
{json.dumps(state.debate_history[-1] if state.debate_history else {}, indent=2)}

MARKET CONDITIONS:
- DTE Choices: {DTE_CHOICES}
"""

        # Add raw data validation context if available
        if raw_data:
            spy_ohlcv = raw_data.get('spy_ohlcv')
            if spy_ohlcv is not None and not spy_ohlcv.empty:
                recent_close = spy_ohlcv['Close'].tail(3)
                price_momentum = "bullish" if recent_close.iloc[-1] > recent_close.iloc[0] else "bearish"
                context += f"\nRAW DATA VALIDATION:\n- SPY 3-day momentum: {price_momentum} (${recent_close.iloc[-1]:.2f})"

            fred_signals = raw_data.get('fred_signals', {})
            if fred_signals and fred_signals.get('values'):
                vix_level = raw_data.get('market_data', {}).get('^VIX', pd.Series()).tail(1)
                if not vix_level.empty:
                    vix_current = vix_level.iloc[-1]
                    vix_regime = "high" if vix_current > 25 else "medium" if vix_current > 15 else "low"
                    context += f"\n- VIX regime: {vix_regime} ({vix_current:.1f})"

        context += f"""

DECISION GUIDANCE (WEIGHTED APPROACH):
- TECHNICAL ANALYSIS (50% weight): This is your PRIMARY decision driver. If Technical Analysis has high confidence and clear direction, it should dominate your decision
- If Technical Analysis confidence is low (<60%), strongly consider NO_TRADE regardless of other factors
- ECONOMIC ANALYSIS (20% weight): Use to confirm or question technical signals
- OPTION PRICING (15% weight): If UNFAVORABLE, strongly consider NO_TRADE unless technical analysis is overwhelmingly strong
- SENTIMENT/NEWS (10% weight): Use as supporting evidence only
- DEBATE OUTCOME (5% weight): Minor influence on final decision

CONFIDENCE CALCULATION:
- Start with Technical Analysis confidence as your base (50% weight)
- Adjust based on alignment/conflict with other weighted analyses
- Reduce confidence significantly if Technical Analysis conflicts with Option Pricing

Make a specific options trading decision for SPY using the weighted framework."""

        prompt = f"""{context}

Return JSON decision:
- action: "BUY_CALL", "BUY_PUT", "SPREAD", or "NO_TRADE"
- strategy: specific strategy name
- dte: chosen DTE from available choices (consider suggested expiry type)
- confidence: 0-10 (factor in option pricing confidence)
- reasoning: detailed explanation including how option pricing analysis influenced your decision
"""

        response = self.generate_response(prompt, is_json=True)

        return TradingDecision(
            action=response.get('action', 'NO_TRADE'),
            strategy=response.get('strategy', 'None'),
            dte=response.get('dte', 30),
            confidence=response.get('confidence', 5),
            reasoning=response.get('reasoning', ''),
            risk_assessment={}
        )

class FundManager(BaseAgent):
    """Final approval authority"""

    def __init__(self):
        prompt = """You are the Fund Manager with final approval authority for all trading decisions.

ROLE & RESPONSIBILITIES:
- Final decision maker for all option trading strategies
- Risk oversight and portfolio protection
- Ensure decisions align with fund objectives and risk tolerance
- Provide clear rationale for approval/rejection/modification decisions

DECISION FRAMEWORK:
1. Evaluate technical and economic analysis quality and consistency
2. Assess risk-reward profile and position sizing appropriateness
3. Consider market conditions and portfolio context
4. Verify confidence levels are well-calibrated and realistic
5. Ensure proper risk management protocols are followed

OUTPUT REQUIREMENTS:
Return a JSON object with:
{
    "decision": "APPROVE|REJECT|MODIFY",
    "confidence": 0.0-1.0,
    "reasoning": "Clear explanation of decision rationale",
    "risk_assessment": "Overall risk evaluation",
    "modifications": ["List any required changes if MODIFY"],
    "position_size_adjustment": 0.0-2.0,
    "uncertainty_factors": ["List key uncertainties affecting decision"]
}

CONFIDENCE CALIBRATION:
- 0.9-1.0: Extremely confident, all analyses align, clear market setup
- 0.7-0.9: High confidence, minor uncertainties or conflicting signals
- 0.5-0.7: Moderate confidence, mixed signals or elevated uncertainty
- 0.3-0.5: Low confidence, significant uncertainties or conflicting analyses
- 0.0-0.3: Very low confidence, major concerns or insufficient information

RISK MANAGEMENT PRIORITIES:
- Preserve capital above all else
- Ensure position sizes are appropriate for volatility
- Consider correlation with existing positions
- Evaluate maximum drawdown scenarios
- Account for liquidity and execution risks

If uncertain about any aspect, acknowledge the uncertainty explicitly and adjust confidence accordingly."""
        super().__init__(DEEP_THINKING_MODEL, prompt, "fund_manager")

    def approve_decision(self, decision: TradingDecision,
                        risk_assessments: List[RiskAssessment],
                        state: AgentState) -> Dict:
        """Final approval or modification of trading decision"""

        context = f"""
PROPOSED DECISION:
{json.dumps(asdict(decision), indent=2)}

RISK ASSESSMENTS:
{json.dumps([asdict(ra) for ra in risk_assessments], indent=2)}

ALL REPORTS:
{state.get_analyst_summary()}

Provide final approval or modifications.
"""

        prompt = f"""{context}

Return final decision as JSON:
- approved: true/false
- final_action: approved action
- final_strategy: approved strategy
- final_dte: approved DTE
- modifications: list of any modifications made
- rationale: explanation of decision
"""

        return self.generate_response(prompt, is_json=True)


class EnhancedFundManager(FundManager):
    """Enhanced Fund Manager with integrated portfolio management and recommendation capabilities"""

    def __init__(self, portfolio_file="my_portfolio.txt"):
        super().__init__()
        self.portfolio_file = portfolio_file
        self.account_size = 10000  # Default $10K account
        self.max_risk_pct = 0.05   # Max 5% risk per trade

    def read_portfolio(self):
        """Read and parse portfolio from text file"""
        if not os.path.exists(self.portfolio_file):
            print(f"📁 Portfolio file not found. Creating template: {self.portfolio_file}")
            self.create_portfolio_template()
            return self._get_empty_portfolio()

        portfolio = self._get_empty_portfolio()

        try:
            with open(self.portfolio_file, 'r') as f:
                lines = f.readlines()

            current_section = None
            for line in lines:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                if line.startswith('[') and line.endswith(']'):
                    current_section = line[1:-1]
                    continue

                if current_section == 'ACCOUNT':
                    if 'Cash Balance:' in line:
                        portfolio['cash_balance'] = float(line.split(':')[1].strip())
                    elif 'Total Account Value:' in line:
                        portfolio['total_value'] = float(line.split(':')[1].strip())
                    elif 'Available for Trading:' in line:
                        portfolio['available_cash'] = float(line.split(':')[1].strip())

                elif current_section == 'POSITIONS':
                    if '|' in line and not line.startswith('#'):
                        parts = [p.strip() for p in line.split('|')]
                        if len(parts) >= 8:
                            position = {
                                'symbol': parts[0],
                                'strike': float(parts[1]),
                                'expiration': parts[2],
                                'type': parts[3].upper(),
                                'quantity': int(parts[4]),
                                'entry_price': float(parts[5]),
                                'current_price': float(parts[6]),
                                'entry_date': parts[7]
                            }
                            portfolio['positions'].append(position)

                elif current_section == 'SETTINGS':
                    if 'Max Risk Per Trade:' in line:
                        self.max_risk_pct = float(line.split(':')[1].strip()) / 100
                    elif 'Account Size:' in line:
                        self.account_size = float(line.split(':')[1].strip())

            print(f"📊 Portfolio loaded: ${portfolio['total_value']:,.2f} total, {len(portfolio['positions'])} positions")
            return portfolio

        except Exception as e:
            print(f"❌ Error reading portfolio: {e}")
            return self._get_empty_portfolio()

    def _get_empty_portfolio(self):
        """Return empty portfolio structure"""
        return {
            'cash_balance': self.account_size,
            'total_value': self.account_size,
            'available_cash': self.account_size,
            'positions': [],
            'trade_history': []
        }

    def create_portfolio_template(self):
        """Create a template portfolio file for user"""
        template = f"""# Personal Options Trading Portfolio
# Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# Instructions: Update this file with your current positions before running the analysis

[ACCOUNT]
Cash Balance: 10000.00
Total Account Value: 10000.00
Available for Trading: 10000.00

[POSITIONS]
# Format: Symbol | Strike | Expiration | Type | Quantity | Entry Price | Current Price | Entry Date
# Example: SPY | 450 | 2025-10-15 | CALL | 1 | 2.50 | 3.20 | 2025-09-20
# (Remove the # to activate a position)

[TRADE_HISTORY]
# Recent trades for performance tracking
# Date | Action | Symbol | Strike | Exp | Type | Qty | Price | P&L
# Example: 2025-09-15 | BUY | SPY | 445 | 2025-09-20 | CALL | 1 | 3.00 | +150.00

[SETTINGS]
# Personal trading preferences
Max Risk Per Trade: 5.0
Account Size: 10000.00
Experience Level: Beginner
"""

        with open(self.portfolio_file, 'w') as f:
            f.write(template)
        print(f"📝 Created portfolio template: {self.portfolio_file}")
        print("📋 Please update this file with your current positions before running analysis")

    def assess_portfolio_risk(self, portfolio):
        """Assess current portfolio risk levels"""
        total_risk = 0
        position_details = []
        recommendations = []

        for position in portfolio['positions']:
            # Calculate position risk (premium at risk)
            position_risk = position['current_price'] * position['quantity'] * 100  # Options are per 100 shares
            total_risk += position_risk

            # Calculate P&L
            pnl = (position['current_price'] - position['entry_price']) * position['quantity'] * 100
            pnl_pct = ((position['current_price'] - position['entry_price']) / position['entry_price']) * 100

            position_details.append({
                'symbol': position['symbol'],
                'type': position['type'],
                'risk': position_risk,
                'pnl': pnl,
                'pnl_pct': pnl_pct,
                'expiration': position['expiration']
            })

            # Check for expiring positions
            try:
                exp_date = datetime.strptime(position['expiration'], '%Y-%m-%d')
                days_to_exp = (exp_date - datetime.now()).days
                if days_to_exp <= 7:
                    recommendations.append(f"⚠️ {position['symbol']} {position['type']} expires in {days_to_exp} days - consider closing")
                elif days_to_exp <= 14:
                    recommendations.append(f"📅 {position['symbol']} {position['type']} expires in {days_to_exp} days - monitor closely")
            except:
                pass

        # Calculate risk percentage
        risk_pct = total_risk / self.account_size if self.account_size > 0 else 0

        # Risk level assessment
        if risk_pct > 0.15:
            risk_level = "HIGH"
            recommendations.append("🔴 Portfolio risk is high - consider reducing position sizes")
        elif risk_pct > 0.10:
            risk_level = "MEDIUM"
            recommendations.append("🟡 Portfolio risk is moderate - monitor positions closely")
        else:
            risk_level = "LOW"

        return {
            'risk_level': risk_level,
            'total_risk': total_risk,
            'risk_percentage': risk_pct * 100,
            'recommendations': recommendations,
            'position_details': position_details
        }

    def generate_unified_recommendation(self, portfolio, market_analysis, risk_assessment,
                                      decision: TradingDecision, risk_assessments: List[RiskAssessment]) -> Dict:
        """Generate unified trading recommendation considering portfolio context and market analysis"""

        # Priority 1: Handle expiring positions
        for position in portfolio['positions']:
            try:
                exp_date = datetime.strptime(position['expiration'], '%Y-%m-%d')
                days_to_exp = (exp_date - datetime.now()).days

                if days_to_exp <= 3:
                    return {
                        'action': 'CLOSE',
                        'symbol': position['symbol'],
                        'type': position['type'],
                        'reasoning': f"Position expires in {days_to_exp} days - immediate closure required",
                        'urgency': 'URGENT',
                        'portfolio_context': f"Closing expiring {position['type']} position"
                    }
                elif days_to_exp <= 7:
                    pnl_pct = self._calculate_position_pnl_pct(position)
                    if pnl_pct > 50:
                        return {
                            'action': 'CLOSE',
                            'symbol': position['symbol'],
                            'type': position['type'],
                            'reasoning': f"Take profits ({pnl_pct:.1f}%) before expiration in {days_to_exp} days",
                            'urgency': 'HIGH',
                            'portfolio_context': f"Profitable {position['type']} position near expiration"
                        }
            except:
                continue

        # Priority 2: Handle losing positions
        for position in portfolio['positions']:
            pnl_pct = self._calculate_position_pnl_pct(position)
            if pnl_pct < -50:  # More than 50% loss
                return {
                    'action': 'CLOSE',
                    'symbol': position['symbol'],
                    'type': position['type'],
                    'reasoning': f"Cut losses ({pnl_pct:.1f}%) to preserve capital",
                    'urgency': 'HIGH',
                    'portfolio_context': f"Losing {position['type']} position exceeds risk tolerance"
                }

        # Priority 3: Handle winning positions
        for position in portfolio['positions']:
            pnl_pct = self._calculate_position_pnl_pct(position)
            if pnl_pct > 75:  # More than 75% gain
                return {
                    'action': 'CLOSE',
                    'symbol': position['symbol'],
                    'type': position['type'],
                    'reasoning': f"Take profits ({pnl_pct:.1f}%) - excellent gain achieved",
                    'urgency': 'MEDIUM',
                    'portfolio_context': f"Highly profitable {position['type']} position"
                }

        # Priority 4: Consider new trades based on market analysis and portfolio capacity
        if risk_assessment['risk_percentage'] < 80:  # Have capacity for new trades
            max_risk = self.account_size * self.max_risk_pct

            # Use the sophisticated trading decision but adjust for portfolio context
            if decision.action in ['BUY_CALL', 'BUY_PUT']:
                return {
                    'action': decision.action,
                    'symbol': 'SPY',
                    'strategy': decision.strategy,
                    'dte': decision.dte,
                    'max_premium': max_risk,
                    'reasoning': f"Market analysis suggests {decision.action}: {decision.reasoning}",
                    'confidence': decision.confidence,
                    'urgency': 'MEDIUM',
                    'portfolio_context': f"Portfolio has capacity for new {decision.action} position",
                    'risk_assessment_summary': f"Risk team average score: {np.mean([ra.risk_score for ra in risk_assessments]):.1f}"
                }

        # Default: No trade recommendation
        return {
            'action': 'NO_TRADE',
            'reasoning': 'No compelling opportunities or portfolio at capacity',
            'urgency': 'LOW',
            'portfolio_context': f"Portfolio risk: {risk_assessment['risk_percentage']:.1f}%"
        }

    def _calculate_position_pnl_pct(self, position):
        """Calculate position P&L percentage"""
        if position['entry_price'] <= 0:
            return 0
        return ((position['current_price'] - position['entry_price']) / position['entry_price']) * 100


    # Optional MCP override: if provided, delegate data retrieval
    # to MCP tool to reduce prompt size and centralize context.
    # Must return a dict matching get_market_data() schema when used.


# --- CORE FRAMEWORK CLASSES ---


class DataFetcher:
    """Handles all external data fetching and pre-processing, including HV calculation."""
    def __init__(self, mcp_context=None):
        self.data_cache = {}
        self.mcp = mcp_context

    def _calculate_hv(self, series: pd.Series) -> pd.Series:
        """Calculates annualized historical volatility."""
        log_returns = np.log(series / series.shift(1))
        hv = log_returns.rolling(window=HV_WINDOW).std() * np.sqrt(252)
        return hv * 100 # Return as percentage

    def get_market_data(self, current_date: datetime) -> dict:
        date_str = current_date.strftime('%Y-%m-%d')
        if date_str in self.data_cache:
            return self.data_cache[date_str]

        # If MCP is provided and implements get_market_data, prefer it
        if hasattr(self, 'mcp') and self.mcp and hasattr(self.mcp, 'get_market_data'):
            try:
                mcp_result = self.mcp.get_market_data(current_date)
                if mcp_result:
                    self.data_cache[date_str] = mcp_result
                    return mcp_result
            except Exception as e:
                logging.warning(f"MCP get_market_data failed, falling back: {e}")

        logging.info(f"Fetching data for window ending on {date_str}...")
        end_date = current_date
        start_date = end_date - timedelta(days=ANALYSIS_WINDOW_DAYS * 1.7)

        try:
            market_data_raw = yf.download(MARKET_TICKERS, start=start_date, end=end_date, progress=False)
            if market_data_raw.empty:
                logging.error(f"No market data found for the period ending {date_str}.")
                return None

            market_data = market_data_raw['Close'].tail(ANALYSIS_WINDOW_DAYS).copy()
            market_data['HV'] = self._calculate_hv(market_data['SPY'])

            # Fetch OHLCV data for SPY for Sperandeo analysis (extended window for technical analysis)
            try:
                spy_ohlcv = get_spy_market_data("SPY", days=126)
                logging.info(f"Fetched SPY OHLCV data: {len(spy_ohlcv)} rows")
            except Exception as e:
                logging.warning(f"Failed to fetch SPY OHLCV data: {e}. Using Close data only.")
                spy_ohlcv = None

            fred_data = web.DataReader(list(FRED_SERIES.keys()), 'fred', start_date, end_date)
            fred_data = fred_data.tail(ANALYSIS_WINDOW_DAYS)

            # Fill NaNs robustly (using newer pandas methods)
            for df in [market_data, fred_data]:
                df.ffill(inplace=True)
                df.bfill(inplace=True)

            if market_data.isnull().values.any() or fred_data.isnull().values.any():
                 logging.critical(f"Unrecoverable NaN values in data for {date_str}. Skipping.")
                 return None

            result = {
                "market_data": market_data,
                "fred_data": fred_data,
                "spy_ohlcv": spy_ohlcv  # Add OHLCV data for Sperandeo analysis
            }
            self.data_cache[date_str] = result
            return result

        except Exception as e:
            logging.error(f"Error fetching data for {date_str}: {e}")
            return None

class EnhancedDataFetcher(DataFetcher):
    """Augmented fetcher that assembles the full TradingAgents data bundle.
    Returns a dict including market_data, fred_data, spy_ohlcv, fred_processed,
    fred_signals, and sperandeo_features.
    """
    def get_comprehensive_data(self, current_date: datetime) -> Optional[dict]:
        base = self.get_market_data(current_date)
        if not base:
            return None
        # Normalize FRED indicators and derive signals (uses FRED API)
        try:
            fred_processed, fred_normalized_only = normalize_fred_indicators(
                api_key=FRED_API_KEY,
                lookback_periods=6,
                end_date=current_date.strftime('%Y-%m-%d')
            )
            fred_signals = get_latest_signals(fred_processed, lookback_days=180)
        except Exception as e:
            logging.warning(f"FRED normalization failed: {e}")
            fred_processed, fred_signals = pd.DataFrame(), {}
        # Generate Sperandeo features if OHLCV available
        features = pd.DataFrame()
        if base.get('spy_ohlcv') is not None and not base['spy_ohlcv'].empty:
            try:
                features = generate_sperandeo_features(base['spy_ohlcv'])
            except Exception as e:
                logging.warning(f"Sperandeo feature generation failed: {e}")
        # Assemble comprehensive payload
        bundle = {
            "market_data": base["market_data"],
            "fred_data": base["fred_data"],
            "spy_ohlcv": base.get("spy_ohlcv"),
            "fred_processed": fred_processed,
            "fred_signals": fred_signals,
            "sperandeo_features": features,
            "asof_date": current_date
        }
        return bundle



@dataclass
class SteeringConfResult:
    """Results from SteeringConf multi-prompt aggregation"""
    selected_decision: TradingDecision
    calibrated_confidence: float
    answer_consistency: float
    confidence_consistency: float
    all_decisions: List[TradingDecision]
    steering_labels: List[str]
    aggregation_metadata: Dict[str, Any]

class SteeringConfChiefTrader(BaseAgent):
    """
    Enhanced Chief Trader implementing the full SteeringConf methodology
    from Zhou et al. (2025) "Calibrating LLM Confidence with Semantic Steering"
    """

    def __init__(self):
        # Initialize with vanilla prompt as base
        prompt = STEERINGCONF_CHIEF_TRADER_PROMPTS["vanilla"]
        super().__init__(DEEP_THINKING_MODEL, prompt, "steeringconf_trader")
        self.aggregator = SteeringConfAggregator()
        self.steering_labels = ["very_cautious", "cautious", "vanilla", "confident", "very_confident"]

    def make_decision(self, state: AgentState, raw_data: Dict = None) -> SteeringConfResult:
        """
        Make trading decision using full SteeringConf methodology with 5 steering prompts
        """
        logging.info("Starting SteeringConf multi-prompt decision making...")

        # Prepare context from all sources (same for all steering prompts)
        context = self._prepare_context(state, raw_data)

        # Execute all 5 steering prompts
        all_decisions = []
        all_confidences = []

        for label in self.steering_labels:
            logging.info(f"Executing {label} steering prompt...")
            decision = self._execute_steering_prompt(label, context)
            all_decisions.append(decision)
            all_confidences.append(decision.confidence)

        # Extract decision strings for consistency analysis
        decision_strings = [d.action for d in all_decisions]

        # Apply SteeringConf aggregation methodology
        dominant_decision, answer_consistency = self.aggregator.calculate_answer_consistency(decision_strings)
        mean_conf, std_conf, confidence_consistency = self.aggregator.calculate_confidence_consistency(all_confidences)
        calibrated_confidence = self.aggregator.calculate_calibrated_confidence(
            mean_conf, answer_consistency, confidence_consistency
        )

        # Select the steered answer closest to calibrated confidence
        selected_decision_str, selected_index = self.aggregator.select_steered_answer(
            decision_strings, all_confidences, calibrated_confidence, self.steering_labels
        )

        selected_decision = all_decisions[selected_index]

        # Create aggregation metadata
        aggregation_metadata = {
            "mean_confidence": mean_conf,
            "std_confidence": std_conf,
            "dominant_decision": dominant_decision,
            "selected_steering": self.steering_labels[selected_index],
            "all_confidences": all_confidences,
            "all_decisions_summary": [
                {"steering": label, "decision": dec.action, "confidence": dec.confidence, "strategy": dec.strategy}
                for label, dec in zip(self.steering_labels, all_decisions)
            ]
        }

        logging.info(f"SteeringConf aggregation complete:")
        logging.info(f"  - Answer consistency: {answer_consistency:.3f}")
        logging.info(f"  - Confidence consistency: {confidence_consistency:.3f}")
        logging.info(f"  - Calibrated confidence: {calibrated_confidence:.3f}")
        logging.info(f"  - Selected steering: {self.steering_labels[selected_index]}")

        return SteeringConfResult(
            selected_decision=selected_decision,
            calibrated_confidence=calibrated_confidence,
            answer_consistency=answer_consistency,
            confidence_consistency=confidence_consistency,
            all_decisions=all_decisions,
            steering_labels=self.steering_labels,
            aggregation_metadata=aggregation_metadata
        )

    def _prepare_context(self, state: AgentState, raw_data: Dict = None) -> str:
        """Prepare the context that will be used for all steering prompts"""
        context = f"""
ANALYST REPORTS:
{state.get_analyst_summary()}

RESEARCHER DEBATE OUTCOME:
{json.dumps(state.debate_history[-1] if state.debate_history else {}, indent=2)}

MARKET CONDITIONS:
- VIX Level: High/Medium/Low
- Option Pricing: Favorable/Unfavorable
- DTE Choices: {DTE_CHOICES}
"""

        # Add raw data validation context if available
        if raw_data:
            spy_ohlcv = raw_data.get('spy_ohlcv')
            if spy_ohlcv is not None and not spy_ohlcv.empty:
                recent_close = spy_ohlcv['Close'].tail(3)
                price_momentum = "bullish" if recent_close.iloc[-1] > recent_close.iloc[0] else "bearish"
                context += f"\nRAW DATA VALIDATION:\n- SPY 3-day momentum: {price_momentum} (${recent_close.iloc[-1]:.2f})"

        context += "\n\nMake a specific options trading decision for SPY."
        return context

    def _execute_steering_prompt(self, steering_label: str, context: str) -> TradingDecision:
        """Execute a single steering prompt and return the trading decision"""
        try:
            # Get the steering-specific prompt
            steering_prompt = STEERINGCONF_CHIEF_TRADER_PROMPTS[steering_label]

            # Create the full prompt with context
            full_prompt = f"{context}\n\n{steering_prompt}"

            # Generate response
            response = self.generate_response(full_prompt, is_json=True)

            # Parse into TradingDecision
            return TradingDecision(
                action=response.get('decision', 'NEUTRAL'),
                strategy=response.get('strategy', 'No Trade'),
                dte=response.get('dte', 30),
                confidence=float(response.get('confidence', 5)),
                reasoning=response.get('reasoning', ''),
                risk_assessment={
                    'analyst_confidence_summary': response.get('analyst_confidence_summary', ''),
                    'uncertainty_factors': response.get('uncertainty_factors', ''),
                    'risk_assessment': response.get('risk_assessment', '')
                }
            )

        except Exception as e:
            logging.error(f"Error in {steering_label} steering prompt: {e}")
            # Return safe default
            return TradingDecision(
                action="NEUTRAL",
                strategy="No Trade",
                dte=30,
                confidence=1.0,
                reasoning=f"Error in {steering_label} prompt: {str(e)}",
                risk_assessment={}
            )


class SperandeoTechnicalAnalyst(LLMAgent):
    """
    Enhanced Technical Analyst using Victor Sperandeo's methodology.
    Extends LLMAgent to include Sperandeo-specific data processing.
    """
    def __init__(self, model: str):
        # Initialize with the technical_analyst prompt from AGENT_PROMPTS
        super().__init__(model, AGENT_PROMPTS["technical_analyst"])

    def analyze(self, raw_data: dict) -> str:
        """
        Perform Sperandeo technical analysis on the provided market data.
        """
        try:
            # Check if we have OHLCV data for SPY
            spy_ohlcv = raw_data.get('spy_ohlcv')
            if spy_ohlcv is None or spy_ohlcv.empty:
                logging.warning("No SPY OHLCV data available. Falling back to basic analysis.")
                return self._fallback_analysis(raw_data)

            # Generate Sperandeo features
            logging.info("Generating Sperandeo technical features...")
            sperandeo_features = generate_sperandeo_features(spy_ohlcv)

            # Construct data context to append to the system prompt
            data_context = construct_sperandeo_data_context(
                spy_ohlcv,
                sperandeo_features,
                raw_data['market_data']
            )

            # Use the standard LLMAgent generate_response method with data context
            logging.info("Requesting Sperandeo technical analysis from LLM...")
            analysis = self.generate_response(data_context)

            if analysis:
                logging.info("Sperandeo technical analysis completed successfully.")
                return analysis
            else:
                logging.error("LLM analysis failed. Using fallback.")
                return self._fallback_analysis(raw_data)

        except Exception as e:
            logging.error(f"Error in Sperandeo technical analysis: {e}")
            return self._fallback_analysis(raw_data)

    def _fallback_analysis(self, raw_data: dict) -> str:
        """
        Fallback to basic technical analysis if Sperandeo analysis fails.
        """
        market_data = raw_data.get('market_data', pd.DataFrame())
        if market_data.empty:
            return "Technical analysis unavailable due to data issues."

        try:
            spy_close = market_data.get('SPY', pd.Series())
            if spy_close.empty:
                return "SPY data unavailable for technical analysis."

            current_price = spy_close.iloc[-1]
            recent_high = spy_close.tail(20).max()
            recent_low = spy_close.tail(20).min()

            # Simple trend analysis
            sma_20 = spy_close.tail(20).mean()
            trend = "Uptrend" if current_price > sma_20 else "Downtrend"

            return f"""**TECHNICAL ANALYSIS (Fallback Mode)**

**Primary Trend:** {trend} - Current price ${current_price:.2f} vs 20-day average ${sma_20:.2f}

**Key Levels:**
- Recent High: ${recent_high:.2f}
- Recent Low: ${recent_low:.2f}
- Support/Resistance: Price is {'above' if current_price > sma_20 else 'below'} key moving average

**Recent Momentum:** {'Bullish' if current_price > sma_20 else 'Bearish'} based on price relative to recent average

Note: This is a simplified analysis due to technical issues with advanced Sperandeo methodology."""

        except Exception as e:
            logging.error(f"Error in fallback analysis: {e}")
            return "Technical analysis temporarily unavailable due to system issues."

class EnhancedBacktestEngine:
    """Orchestrates the complete TradingAgents workflow"""

    def __init__(self, start_date: str, end_date: str, mcp_context: Any = None, use_steeringconf: bool = False):
        self.start_date = datetime.strptime(start_date, '%Y-%m-%d')
        self.end_date = datetime.strptime(end_date, '%Y-%m-%d')
        self.data_fetcher = EnhancedDataFetcher(mcp_context=mcp_context)
        self.results = []
        self.use_steeringconf = use_steeringconf

        # Initialize all agents
        self.technical_analyst = TechnicalAnalystAgent()
        self.economist = EconomistSentimentAnalystAgent()

        # Initialize Alpha Vantage–powered analysts
        self.sentiment_analyst = AlphaVantageSentimentAnalyst(ALPHAVANTAGE_API_KEY)
        self.news_analyst = AlphaVantageNewsAnalyst(ALPHAVANTAGE_API_KEY)

        # Researcher team with enhanced prompts for raw data analysis
        self.bull_researcher = BaseAgent(
            DEEP_THINKING_MODEL,
            """You are a bullish researcher advocating for positive opportunities.

CAPABILITIES:
- Analyze raw SPY OHLCV data for bullish technical patterns
- Interpret FRED economic indicators for positive economic signals
- Form independent opinions beyond analyst summaries
- Identify opportunities that primary analysts might have missed

APPROACH:
- Use raw data to validate or challenge analyst conclusions
- Look for bullish divergences, support levels, and positive momentum
- Consider economic tailwinds and improving fundamentals
- Provide specific data points to support your bullish thesis""",
            "bull"
        )
        self.bear_researcher = BaseAgent(
            DEEP_THINKING_MODEL,
            """You are a bearish researcher highlighting risks and concerns.

CAPABILITIES:
- Analyze raw SPY OHLCV data for bearish technical patterns
- Interpret FRED economic indicators for negative economic signals
- Form independent opinions beyond analyst summaries
- Identify risks that primary analysts might have overlooked

APPROACH:
- Use raw data to validate or challenge analyst conclusions
- Look for bearish divergences, resistance levels, and negative momentum
- Consider economic headwinds and deteriorating fundamentals
- Provide specific data points to support your bearish thesis""",
            "bear"
        )
        self.debate_facilitator = DebateFacilitator()

        # Trading team - choose between standard and SteeringConf
        if use_steeringconf:
            self.chief_trader = SteeringConfChiefTrader()
            logging.info("Using SteeringConf multi-prompt Chief Trader")
        else:
            self.chief_trader = ChiefTrader()
            logging.info("Using standard Chief Trader")

        self.risk_team = RiskManagementTeam()
        self.fund_manager = FundManager()

        logging.info(f"Enhanced BacktestEngine initialized with {'SteeringConf' if use_steeringconf else 'standard'} configuration")

    def run_simulation_step(self, current_date: datetime):
        """Execute complete TradingAgents workflow for one day"""
        logging.info(f"--- Running TradingAgents simulation for {current_date.strftime('%Y-%m-%d')} ---")

        # Initialize state
        state = AgentState()

        # 1. Data Collection
        data = self.data_fetcher.get_comprehensive_data(current_date)
        if not data:
            logging.error("Failed to fetch data, skipping day")
            return

        # 2. Analyst Phase (Concurrent Analysis)
        logging.info("Phase 1: Analyst Team gathering information...")

        # Technical Analysis
        tech_report = self.technical_analyst.analyze(data, state)
        state.add_analyst_report(tech_report)

        # Economic Analysis
        econ_report = self.economist.analyze(data, state)
        state.add_analyst_report(econ_report)

        # Sentiment and News Analysis (Alpha Vantage)
        try:
            sent_report = self.sentiment_analyst.analyze(data, state)
            state.add_analyst_report(sent_report)
        except Exception as e:
            logging.warning(f"Sentiment analysis failed: {e}")
        try:
            news_report = self.news_analyst.analyze(data, state)
            state.add_analyst_report(news_report)
        except Exception as e:
            logging.warning(f"News analysis failed: {e}")

        logging.info("Analyst reports completed")
        # Persist analyst reports into structured protocol
        state.record_protocol("analyst_reports", {k: asdict(v) for k, v in state.analyst_reports.items()})


        # 3. Researcher Phase (Debate)
        logging.info("Phase 2: Researcher Team debate...")

        debate_context = state.get_analyst_summary()
        debate_result = self.debate_facilitator.facilitate_debate(
            self.bull_researcher,
            self.bear_researcher,
            debate_context,
            raw_data=data,  # Pass raw data for independent analysis
            rounds=DEBATE_ROUNDS
        )

        state.debate_history = debate_result['debate_history']
        logging.info(f"Debate concluded: {debate_result['result'].get('prevailing_view')}")
        # Persist debate outcome
        state.record_protocol("research_debate", debate_result.get('result', {}))


        # 4. Trading Decision Phase
        if self.use_steeringconf:
            logging.info("Phase 3: SteeringConf Chief Trader making multi-prompt decision...")
            steeringconf_result = self.chief_trader.make_decision(state, raw_data=data)
            trading_decision = steeringconf_result.selected_decision

            # Persist SteeringConf results
            state.record_protocol("steeringconf_decision", {
                "selected_decision": asdict(trading_decision),
                "calibrated_confidence": steeringconf_result.calibrated_confidence,
                "answer_consistency": steeringconf_result.answer_consistency,
                "confidence_consistency": steeringconf_result.confidence_consistency,
                "aggregation_metadata": steeringconf_result.aggregation_metadata
            })

            logging.info(f"SteeringConf decision: {trading_decision.action} - {trading_decision.strategy}")
            logging.info(f"Calibrated confidence: {steeringconf_result.calibrated_confidence:.3f}")
        else:
            logging.info("Phase 3: Chief Trader making decision...")
            trading_decision = self.chief_trader.make_decision(state, raw_data=data)
            # Persist trader decision
            state.record_protocol("trader_decision", asdict(trading_decision))
            logging.info(f"Trading decision: {trading_decision.action} - {trading_decision.strategy}")

        state.trading_decision = trading_decision

        # 5. Risk Management Phase
        logging.info("Phase 4: Risk Management Team assessment...")

        risk_assessments = self.risk_team.assess_decision(trading_decision, state, raw_data=data)
        state.risk_assessments = risk_assessments
        # Persist risk assessments
        state.record_protocol("risk_assessments", [asdict(ra) for ra in risk_assessments])


        avg_risk = np.mean([ra.risk_score for ra in risk_assessments])
        logging.info(f"Average risk score: {avg_risk:.2f}")

        # 6. Fund Manager Approval
        logging.info("Phase 5: Fund Manager final approval...")

        final_decision = self.fund_manager.approve_decision(
            trading_decision,
            risk_assessments,
            state
        )
        state.final_decision = final_decision
        # Persist final decision
        state.record_protocol("final_decision", final_decision)


        logging.info(f"Final decision: {'APPROVED' if final_decision.get('approved') else 'MODIFIED'}")

        # 7. Log Results
        self.results.append({
            "date": current_date.strftime('%Y-%m-%d'),
            "analyst_reports": {k: asdict(v) for k, v in state.analyst_reports.items()},
            "debate_outcome": debate_result['result'],
            "trading_decision": asdict(trading_decision),
            "risk_assessments": [asdict(ra) for ra in risk_assessments],
            "final_decision": final_decision
        })

    def run_backtest(self):
        """Execute complete backtest over date range"""
        all_dates = pd.bdate_range(self.start_date, self.end_date)

        for current_date in all_dates:
            try:
                self.run_simulation_step(current_date)
                time.sleep(0.5)  # Rate limiting
            except Exception as e:
                logging.error(f"Error on {current_date}: {e}")
                continue

        logging.info("--- TradingAgents backtest complete ---")
        self.save_results()

    def save_results(self):
        """Save structured results"""
        if not self.results:
            logging.warning("No results to save")
            return

        # Save as JSON for structured data
        filename = f"tradingagents_v3_results_{self.start_date.strftime('%Y%m%d')}_{self.end_date.strftime('%Y%m%d')}.json"
        with open(filename, 'w') as f:
            json.dump(self.results, f, indent=2)

        # Also save summary as CSV
        summary_data = []
        for result in self.results:
            summary_data.append({
                'date': result['date'],
                'action': result['trading_decision']['action'],
                'strategy': result['trading_decision']['strategy'],
                'dte': result['trading_decision']['dte'],
                'confidence': result['trading_decision']['confidence'],
                'approved': result['final_decision'].get('approved', False),
                'tech_rec': result['analyst_reports']['technical']['recommendation'],
                'econ_rec': result['analyst_reports']['economist']['recommendation']
            })

        pd.DataFrame(summary_data).to_csv(
            f"tradingagents_v3_summary_{self.start_date.strftime('%Y%m%d')}_{self.end_date.strftime('%Y%m%d')}.csv",
            index=False
        )

        logging.info(f"Results saved to {filename}")


# --- STEERINGCONF TESTING FUNCTIONS ---

def test_steeringconf_aggregation():
    """Test the SteeringConf aggregation algorithms"""
    logging.info("Testing SteeringConf aggregation algorithms...")

    aggregator = SteeringConfAggregator()

    # Test answer consistency
    decisions = ["BULLISH", "BULLISH", "NEUTRAL", "BULLISH", "BEARISH"]
    dominant, consistency = aggregator.calculate_answer_consistency(decisions)
    logging.info(f"Answer consistency test: dominant={dominant}, consistency={consistency:.3f}")

    # Test confidence consistency
    confidences = [7.5, 8.0, 6.5, 7.8, 5.2]
    mean_conf, std_conf, conf_consistency = aggregator.calculate_confidence_consistency(confidences)
    logging.info(f"Confidence consistency test: mean={mean_conf:.3f}, std={std_conf:.3f}, consistency={conf_consistency:.3f}")

    # Test calibrated confidence
    calibrated = aggregator.calculate_calibrated_confidence(mean_conf, consistency, conf_consistency)
    logging.info(f"Calibrated confidence: {calibrated:.3f}")

    # Test steered answer selection
    selected_decision, selected_index = aggregator.select_steered_answer(
        decisions, confidences, calibrated, ["very_cautious", "cautious", "vanilla", "confident", "very_confident"]
    )
    logging.info(f"Selected decision: {selected_decision} (index {selected_index})")

    logging.info("SteeringConf aggregation tests completed successfully!")
    return True

# --- PERSONAL TRADING FEATURES ---

class PersonalPortfolioManager:
    """Manages personal portfolio from text file for simplified trading"""

    def __init__(self, portfolio_file="my_portfolio.txt"):
        self.portfolio_file = portfolio_file
        self.account_size = 10000  # Default $10K account
        self.max_risk_pct = 0.05   # Max 5% risk per trade

    def create_portfolio_template(self):
        """Create a template portfolio file for user"""
        template = f"""# Personal Options Trading Portfolio
# Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# Instructions: Update this file with your current positions before running the analysis

[ACCOUNT]
Cash Balance: 10000.00
Total Account Value: 10000.00
Available for Trading: 10000.00

[POSITIONS]
# Format: Symbol | Strike | Expiration | Type | Quantity | Entry Price | Current Price | Entry Date
# Example: SPY | 450 | 2025-10-15 | CALL | 1 | 2.50 | 3.20 | 2025-09-20
# (Remove the # to activate a position)

[TRADE_HISTORY]
# Recent trades for performance tracking
# Date | Action | Symbol | Strike | Exp | Type | Qty | Price | P&L
# Example: 2025-09-15 | BUY | SPY | 445 | 2025-09-20 | CALL | 1 | 3.00 | +150.00

[SETTINGS]
# Personal trading preferences
Max Risk Per Trade: 5.0
Account Size: 10000.00
Experience Level: Beginner
"""

        with open(self.portfolio_file, 'w') as f:
            f.write(template)
        print(f"📝 Created portfolio template: {self.portfolio_file}")
        print("📋 Please update this file with your current positions before running analysis")

    def read_portfolio(self):
        """Read and parse portfolio from text file"""
        if not os.path.exists(self.portfolio_file):
            print(f"📁 Portfolio file not found. Creating template: {self.portfolio_file}")
            self.create_portfolio_template()
            return self._get_empty_portfolio()

        portfolio = self._get_empty_portfolio()

        try:
            with open(self.portfolio_file, 'r') as f:
                lines = f.readlines()

            current_section = None
            for line in lines:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                if line.startswith('[') and line.endswith(']'):
                    current_section = line[1:-1]
                    continue

                if current_section == 'ACCOUNT':
                    if 'Cash Balance:' in line:
                        portfolio['cash'] = float(line.split(':')[1].strip())
                    elif 'Total Account Value:' in line:
                        portfolio['total_value'] = float(line.split(':')[1].strip())
                        self.account_size = portfolio['total_value']

                elif current_section == 'POSITIONS':
                    if '|' in line and not line.startswith('#'):
                        parts = [p.strip() for p in line.split('|')]
                        if len(parts) >= 8:
                            position = {
                                'symbol': parts[0],
                                'strike': float(parts[1]),
                                'expiration': parts[2],
                                'type': parts[3].upper(),
                                'quantity': int(parts[4]),
                                'entry_price': float(parts[5]),
                                'current_price': float(parts[6]),
                                'entry_date': parts[7]
                            }
                            portfolio['positions'].append(position)

                elif current_section == 'SETTINGS':
                    if 'Max Risk Per Trade:' in line:
                        self.max_risk_pct = float(line.split(':')[1].strip()) / 100
                    elif 'Account Size:' in line:
                        self.account_size = float(line.split(':')[1].strip())

            print(f"📊 Portfolio loaded: ${portfolio['total_value']:,.2f} total, {len(portfolio['positions'])} positions")
            return portfolio

        except Exception as e:
            print(f"❌ Error reading portfolio: {e}")
            print("📝 Please check the format of your portfolio file")
            return self._get_empty_portfolio()

    def _get_empty_portfolio(self):
        """Return empty portfolio structure"""
        return {
            'cash': self.account_size,
            'total_value': self.account_size,
            'positions': [],
            'trade_history': []
        }

    def assess_portfolio_risk(self, portfolio):
        """Assess current portfolio risk for personal account"""
        if not portfolio['positions']:
            return {
                'risk_level': 'LOW',
                'total_risk': 0,
                'risk_percentage': 0,
                'recommendations': ['✅ No current positions - ready for new trades'],
                'position_details': []
            }

        total_risk = 0
        recommendations = []
        position_details = []

        for position in portfolio['positions']:
            # Calculate position value and risk
            position_value = position['current_price'] * position['quantity'] * 100  # Options are per 100 shares
            entry_value = position['entry_price'] * position['quantity'] * 100
            unrealized_pnl = position_value - entry_value
            unrealized_pnl_pct = (unrealized_pnl / entry_value) * 100 if entry_value > 0 else 0

            total_risk += position_value

            # Check expiration
            try:
                exp_date = datetime.strptime(position['expiration'], '%Y-%m-%d')
                days_to_exp = (exp_date - datetime.now()).days

                position_detail = {
                    'symbol': position['symbol'],
                    'strike': position['strike'],
                    'type': position['type'],
                    'days_to_exp': days_to_exp,
                    'unrealized_pnl': unrealized_pnl,
                    'unrealized_pnl_pct': unrealized_pnl_pct,
                    'current_value': position_value
                }
                position_details.append(position_detail)

                if days_to_exp <= 7:
                    recommendations.append(f"🚨 {position['symbol']} ${position['strike']} {position['type']} expires in {days_to_exp} days - URGENT: Consider closing (P&L: {unrealized_pnl_pct:+.1f}%)")
                elif days_to_exp <= 14:
                    recommendations.append(f"⚠️  {position['symbol']} ${position['strike']} {position['type']} expires in {days_to_exp} days - Monitor closely (P&L: {unrealized_pnl_pct:+.1f}%)")
                elif unrealized_pnl_pct > 50:
                    recommendations.append(f"💰 {position['symbol']} ${position['strike']} {position['type']} up {unrealized_pnl_pct:+.1f}% - Consider taking profits")
                elif unrealized_pnl_pct < -50:
                    recommendations.append(f"📉 {position['symbol']} ${position['strike']} {position['type']} down {unrealized_pnl_pct:+.1f}% - Consider cutting losses")

            except:
                recommendations.append(f"⚠️  Check expiration date format for {position['symbol']} (use YYYY-MM-DD)")

        risk_pct = total_risk / portfolio['total_value'] if portfolio['total_value'] > 0 else 0

        if risk_pct > 0.25:  # More than 25% in options
            risk_level = 'EXTREME'
            recommendations.append("🚨 EXTREME options exposure - Strongly consider reducing positions")
        elif risk_pct > 0.15:  # More than 15% in options
            risk_level = 'HIGH'
            recommendations.append("⚠️  HIGH options exposure - Consider reducing some positions")
        elif risk_pct > 0.08:  # More than 8% in options
            risk_level = 'MEDIUM'
            recommendations.append("📊 MODERATE options exposure - Monitor carefully")
        else:
            risk_level = 'LOW'
            recommendations.append("✅ Reasonable options exposure for account size")

        return {
            'risk_level': risk_level,
            'total_risk': total_risk,
            'risk_percentage': risk_pct * 100,
            'recommendations': recommendations,
            'position_details': position_details
        }

class PersonalTradingRecommendation:
    """Generates simplified, actionable trading recommendations for personal accounts"""

    def __init__(self, portfolio_manager: PersonalPortfolioManager):
        self.portfolio_manager = portfolio_manager

    def generate_single_recommendation(self, portfolio, market_analysis, risk_assessment):
        """Generate ONE clear, actionable recommendation"""

        # Priority 1: Handle expiring positions
        for position in portfolio['positions']:
            try:
                exp_date = datetime.strptime(position['expiration'], '%Y-%m-%d')
                days_to_exp = (exp_date - datetime.now()).days

                if days_to_exp <= 7:
                    unrealized_pnl_pct = self._calculate_position_pnl_pct(position)
                    return {
                        'action': 'SELL',
                        'symbol': position['symbol'],
                        'strike': position['strike'],
                        'expiration': position['expiration'],
                        'type': position['type'],
                        'quantity': position['quantity'],
                        'reasoning': f"🚨 URGENT: Position expires in {days_to_exp} days. Current P&L: {unrealized_pnl_pct:+.1f}%",
                        'risk_reward': f"Avoid time decay risk. Position expires {position['expiration']}.",
                        'urgency': 'CRITICAL',
                        'beginner_explanation': "Options lose value rapidly as they approach expiration. It's usually better to close positions before they expire worthless, even at a loss."
                    }
            except:
                continue

        # Priority 2: Handle high-risk portfolio
        if risk_assessment['risk_level'] in ['EXTREME', 'HIGH']:
            if portfolio['positions']:
                # Find worst performing position
                worst_position = None
                worst_pnl = float('inf')

                for position in portfolio['positions']:
                    pnl_pct = self._calculate_position_pnl_pct(position)
                    if pnl_pct < worst_pnl:
                        worst_pnl = pnl_pct
                        worst_position = position

                if worst_position:
                    return {
                        'action': 'SELL',
                        'symbol': worst_position['symbol'],
                        'strike': worst_position['strike'],
                        'expiration': worst_position['expiration'],
                        'type': worst_position['type'],
                        'quantity': worst_position['quantity'],
                        'reasoning': f"Portfolio risk too high ({risk_assessment['risk_level']}). Closing worst position (P&L: {worst_pnl:+.1f}%)",
                        'risk_reward': f"Reduce portfolio risk from {risk_assessment['risk_percentage']:.1f}% to safer levels",
                        'urgency': 'HIGH',
                        'beginner_explanation': f"Your options exposure is {risk_assessment['risk_percentage']:.1f}% of your account, which is too high. It's safer to reduce risk by closing losing positions."
                    }

        # Priority 3: Take profits on big winners
        for position in portfolio['positions']:
            pnl_pct = self._calculate_position_pnl_pct(position)
            if pnl_pct > 100:  # More than 100% gain
                return {
                    'action': 'SELL',
                    'symbol': position['symbol'],
                    'strike': position['strike'],
                    'expiration': position['expiration'],
                    'type': position['type'],
                    'quantity': position['quantity'],
                    'reasoning': f"💰 Excellent profit opportunity! Position up {pnl_pct:+.1f}%",
                    'risk_reward': f"Lock in ${(position['current_price'] - position['entry_price']) * position['quantity'] * 100:.0f} profit",
                    'urgency': 'MEDIUM',
                    'beginner_explanation': "Taking profits at 100%+ gains is a good practice. Options can lose value quickly, so it's often wise to secure profits when available."
                }

        # Priority 4: Generate new trade recommendation based on market analysis
        return self._generate_new_trade_recommendation(portfolio, market_analysis, risk_assessment)

    def _calculate_position_pnl_pct(self, position):
        """Calculate position P&L percentage"""
        if position['entry_price'] <= 0:
            return 0
        return ((position['current_price'] - position['entry_price']) / position['entry_price']) * 100

    def _generate_new_trade_recommendation(self, portfolio, market_analysis, risk_assessment):
        """Generate new trade recommendation based on market conditions"""

        # Check if we have room for new trades
        max_risk = self.portfolio_manager.account_size * self.portfolio_manager.max_risk_pct
        current_risk_pct = risk_assessment['risk_percentage']

        if current_risk_pct > 10:  # Already have >10% in options
            return {
                'action': 'HOLD',
                'reasoning': f"Current options exposure ({current_risk_pct:.1f}%) is sufficient. Wait for better opportunities or close existing positions.",
                'risk_reward': "Patience is key in options trading. Wait for high-probability setups.",
                'urgency': 'LOW',
                'beginner_explanation': "It's better to wait for clear market signals than to overtrade. Focus on quality over quantity."
            }

        # Simplified market signal interpretation
        # This would integrate with the existing sophisticated analysis
        market_signal = self._interpret_market_signals(market_analysis)

        if market_signal['direction'] == 'BULLISH' and market_signal['strength'] > 0.7:
            return self._recommend_call_option(max_risk, market_signal)
        elif market_signal['direction'] == 'BEARISH' and market_signal['strength'] > 0.7:
            return self._recommend_put_option(max_risk, market_signal)
        else:
            return {
                'action': 'HOLD',
                'reasoning': f"Market signals are mixed or weak. {market_signal.get('explanation', 'Wait for clearer direction.')}",
                'risk_reward': "Preserve capital and wait for high-probability opportunities",
                'urgency': 'LOW',
                'beginner_explanation': "Mixed market signals suggest waiting. It's better to miss an opportunity than to lose money on unclear setups."
            }

    def _interpret_market_signals(self, market_analysis):
        """Simplified interpretation of the sophisticated market analysis"""
        try:
            signals = []
            explanations = []

            # 1. Trend Analysis
            trend = market_analysis.get('trend', 'NEUTRAL')
            if trend == 'BULLISH':
                signals.append(1)
                explanations.append("Upward price trend detected")
            elif trend == 'BEARISH':
                signals.append(-1)
                explanations.append("Downward price trend detected")
            else:
                signals.append(0)
                explanations.append("Sideways price action")

            # 2. Volatility Analysis
            vix_level = market_analysis.get('vix_level', 0)
            volatility_regime = market_analysis.get('volatility_regime', 'UNKNOWN')

            if volatility_regime == 'LOW':
                signals.append(1)  # Low vol good for buying options
                explanations.append(f"Low volatility (VIX {vix_level:.1f}) - options relatively cheap")
            elif volatility_regime == 'HIGH':
                signals.append(-1)  # High vol = expensive options
                explanations.append(f"High volatility (VIX {vix_level:.1f}) - options expensive, market stress")
            else:
                signals.append(0)
                explanations.append(f"Normal volatility (VIX {vix_level:.1f})")

            # 3. Sperandeo Features (if available)
            sperandeo_features = market_analysis.get('sperandeo_features', pd.DataFrame())
            if not sperandeo_features.empty:
                # Look for recent signals in Sperandeo features
                try:
                    latest_features = sperandeo_features.iloc[-1]

                    # Check for bullish patterns
                    bullish_signals = 0
                    bearish_signals = 0

                    # 1-2-3 Rule
                    if latest_features.get('rule_123_bullish', False):
                        bullish_signals += 1
                    if latest_features.get('rule_123_bearish', False):
                        bearish_signals += 1

                    # 2B Pattern
                    if latest_features.get('pattern_2b_bullish', False):
                        bullish_signals += 1
                    if latest_features.get('pattern_2b_bearish', False):
                        bearish_signals += 1

                    # Four Day Rule
                    if latest_features.get('four_day_rule_bullish', False):
                        bullish_signals += 1
                    if latest_features.get('four_day_rule_bearish', False):
                        bearish_signals += 1

                    if bullish_signals > bearish_signals:
                        signals.append(1)
                        explanations.append(f"Sperandeo analysis shows {bullish_signals} bullish patterns")
                    elif bearish_signals > bullish_signals:
                        signals.append(-1)
                        explanations.append(f"Sperandeo analysis shows {bearish_signals} bearish patterns")
                    else:
                        signals.append(0)
                        explanations.append("Sperandeo analysis shows mixed signals")

                except Exception as e:
                    signals.append(0)
                    explanations.append("Sperandeo analysis inconclusive")

            # Calculate overall signal
            total_signal = sum(signals)
            signal_count = len(signals)

            if total_signal >= 2:
                direction = 'BULLISH'
                strength = min(total_signal / signal_count, 1.0)
            elif total_signal <= -2:
                direction = 'BEARISH'
                strength = min(abs(total_signal) / signal_count, 1.0)
            else:
                direction = 'NEUTRAL'
                strength = 0.5

            return {
                'direction': direction,
                'strength': strength,
                'explanation': '. '.join(explanations),
                'signal_breakdown': {
                    'trend': trend,
                    'volatility': volatility_regime,
                    'vix_level': vix_level,
                    'total_signals': len(signals),
                    'bullish_signals': sum(1 for s in signals if s > 0),
                    'bearish_signals': sum(1 for s in signals if s < 0)
                }
            }

        except Exception as e:
            return {
                'direction': 'NEUTRAL',
                'strength': 0.5,
                'explanation': f'Signal analysis error: {str(e)}',
                'error': True
            }

    def _recommend_call_option(self, max_risk, market_signal):
        """Recommend a call option trade"""
        return {
            'action': 'BUY',
            'symbol': 'SPY',
            'type': 'CALL',
            'strike': 'ATM+2',  # Slightly out of the money
            'expiration': '3-4 weeks',
            'max_premium': max_risk,
            'reasoning': f"Bullish market signals detected. {market_signal.get('explanation', '')}",
            'risk_reward': f"Risk: Up to ${max_risk:.0f} ({self.portfolio_manager.max_risk_pct*100:.0f}% of account). Target: 50-100% gain.",
            'urgency': 'MEDIUM',
            'beginner_explanation': f"Call options profit when the stock price goes up. Risk only the premium paid (${max_risk:.0f}). Set profit target at 50-100% gain."
        }

    def _recommend_put_option(self, max_risk, market_signal):
        """Recommend a put option trade"""
        return {
            'action': 'BUY',
            'symbol': 'SPY',
            'type': 'PUT',
            'strike': 'ATM-2',  # Slightly out of the money
            'expiration': '3-4 weeks',
            'max_premium': max_risk,
            'reasoning': f"Bearish market signals detected. {market_signal.get('explanation', '')}",
            'risk_reward': f"Risk: Up to ${max_risk:.0f} ({self.portfolio_manager.max_risk_pct*100:.0f}% of account). Target: 50-100% gain.",
            'urgency': 'MEDIUM',
            'beginner_explanation': f"Put options profit when the stock price goes down. Risk only the premium paid (${max_risk:.0f}). Set profit target at 50-100% gain."
        }

class PersonalTradingSystem:
    """Main personal trading system with unified EnhancedFundManager"""

    def __init__(self, portfolio_file="my_portfolio.txt"):
        # Use EnhancedFundManager for unified portfolio and decision management
        self.fund_manager = EnhancedFundManager(portfolio_file)

        # Initialize the sophisticated analysis components
        self.technical_analyst = TechnicalAnalystAgent()
        self.economist = EconomistSentimentAnalystAgent()
        self.chief_trader = ChiefTrader()
        self.risk_team = RiskManagementTeam()

        print("🚀 Personal Options Trading System Initialized")
        print("📊 Unified portfolio-aware decision making with EnhancedFundManager")
        print("🎯 Sophisticated analysis with portfolio context integration")

    def run_daily_analysis(self):
        """Run complete daily analysis with simplified output"""
        print("\n" + "="*70)
        print("🎯 DAILY PERSONAL OPTIONS TRADING ANALYSIS")
        print(f"📅 {datetime.now().strftime('%A, %B %d, %Y at %H:%M:%S')}")
        print("="*70)

        try:
            # Step 1: Load personal portfolio
            print("\n📁 STEP 1: Loading Personal Portfolio...")
            portfolio = self.fund_manager.read_portfolio()

            # Step 2: Run sophisticated market analysis with portfolio context
            print("\n🔍 STEP 2: Running Sophisticated Market Analysis...")
            market_analysis, raw_data = self._run_market_analysis()

            # Step 3: Assess portfolio risk
            print("\n⚖️  STEP 3: Assessing Portfolio Risk...")
            risk_assessment = self.fund_manager.assess_portfolio_risk(portfolio)

            # Step 4: Run sophisticated trading decision process
            print("\n🧠 STEP 4: Running Sophisticated Trading Decision Process...")
            state = AgentState()

            # Technical analysis
            tech_report = self.technical_analyst.analyze(raw_data, state)
            state.add_analyst_report(tech_report)

            # Economic analysis
            econ_report = self.economist.analyze(raw_data, state)
            state.add_analyst_report(econ_report)

            # Trading decision
            trading_decision = self.chief_trader.make_decision(state, raw_data=raw_data)

            # Risk assessment
            risk_assessments = self.risk_team.assess_decision(trading_decision, state, raw_data=raw_data)

            # Step 5: Generate unified recommendation with portfolio context
            print("\n🎯 STEP 5: Generating Unified Portfolio-Aware Recommendation...")
            recommendation = self.fund_manager.generate_unified_recommendation(
                portfolio, market_analysis, risk_assessment, trading_decision, risk_assessments
            )

            # Step 6: Display results in beginner-friendly format
            self._display_results(portfolio, risk_assessment, recommendation, market_analysis, trading_decision, risk_assessments)

        except Exception as e:
            print(f"\n❌ Error in daily analysis: {e}")
            print("📝 Please check your portfolio file format and try again")
            traceback.print_exc()

    def _run_market_analysis(self):
        """Run the sophisticated market analysis using existing components"""
        try:
            # Use existing data fetching capabilities
            data_fetcher = EnhancedDataFetcher()
            current_date = datetime.now()

            # Get comprehensive market data using existing method
            comprehensive_data = data_fetcher.get_comprehensive_data(current_date)

            if comprehensive_data:
                print("   ✅ Market data fetched successfully")

                # Extract data components
                spy_data = comprehensive_data.get('spy_ohlcv', pd.DataFrame())
                sperandeo_features = comprehensive_data.get('sperandeo_features', pd.DataFrame())
                fred_data = comprehensive_data.get('fred_data', {})

                print("   ✅ Technical analysis completed")

                # Get current prices
                current_spy = spy_data['Close'].iloc[-1] if not spy_data.empty else 0

                # Get VIX data separately since it's not in comprehensive data
                try:
                    vix_ticker = yf.Ticker("^VIX")
                    vix_data = vix_ticker.history(period="5d")
                    current_vix = vix_data['Close'].iloc[-1] if not vix_data.empty else 0
                except:
                    current_vix = 0

                # Basic trend analysis
                if len(spy_data) >= 5:
                    recent_trend = 'BULLISH' if spy_data['Close'].iloc[-1] > spy_data['Close'].iloc[-5] else 'BEARISH'
                else:
                    recent_trend = 'NEUTRAL'

                # VIX interpretation
                if current_vix < 15:
                    volatility_regime = 'LOW'
                elif current_vix > 25:
                    volatility_regime = 'HIGH'
                else:
                    volatility_regime = 'NORMAL'

                market_analysis = {
                    'spy_price': current_spy,
                    'vix_level': current_vix,
                    'trend': recent_trend,
                    'volatility_regime': volatility_regime,
                    'sperandeo_features': sperandeo_features,
                    'fred_data': fred_data,
                    'comprehensive_data': comprehensive_data
                }

                print(f"   📊 SPY: ${current_spy:.2f} | VIX: {current_vix:.2f} | Trend: {recent_trend} | Vol: {volatility_regime}")

                return market_analysis, comprehensive_data  # Return both analysis and raw data
            else:
                # Fallback to basic data fetching
                print("   ⚠️  Using fallback market data...")
                basic_data = self._get_basic_market_data()
                return basic_data, None

        except Exception as e:
            print(f"   ⚠️  Market analysis error: {e}")
            print("   🔄 Attempting fallback market data...")
            basic_data = self._get_basic_market_data()
            return basic_data, None

    def _get_basic_market_data(self):
        """Fallback method for basic market data"""
        try:
            # Get basic SPY and VIX data
            spy_ticker = yf.Ticker("SPY")
            vix_ticker = yf.Ticker("^VIX")

            spy_data = spy_ticker.history(period="1mo")
            vix_data = vix_ticker.history(period="5d")

            current_spy = spy_data['Close'].iloc[-1] if not spy_data.empty else 0
            current_vix = vix_data['Close'].iloc[-1] if not vix_data.empty else 0

            # Basic trend analysis
            if len(spy_data) >= 5:
                recent_trend = 'BULLISH' if spy_data['Close'].iloc[-1] > spy_data['Close'].iloc[-5] else 'BEARISH'
            else:
                recent_trend = 'NEUTRAL'

            # VIX interpretation
            if current_vix < 15:
                volatility_regime = 'LOW'
            elif current_vix > 25:
                volatility_regime = 'HIGH'
            else:
                volatility_regime = 'NORMAL'

            print(f"   📊 SPY: ${current_spy:.2f} | VIX: {current_vix:.2f} | Trend: {recent_trend} | Vol: {volatility_regime}")

            return {
                'spy_price': current_spy,
                'vix_level': current_vix,
                'trend': recent_trend,
                'volatility_regime': volatility_regime,
                'sperandeo_features': pd.DataFrame(),
                'fred_data': {},
                'fallback_mode': True
            }

        except Exception as e:
            print(f"   ❌ Fallback market data failed: {e}")
            return {
                'spy_price': 0,
                'vix_level': 0,
                'trend': 'NEUTRAL',
                'volatility_regime': 'UNKNOWN',
                'error': str(e)
            }

    def _display_results(self, portfolio, risk_assessment, recommendation, market_analysis,
                        trading_decision=None, risk_assessments=None):
        """Display results in beginner-friendly format with enhanced analysis"""

        # Portfolio Summary
        print(f"\n📊 PORTFOLIO SUMMARY:")
        print(f"   💰 Total Account Value: ${portfolio['total_value']:,.2f}")
        print(f"   💵 Cash Available: ${portfolio.get('available_cash', portfolio.get('cash_balance', 0)):,.2f}")
        print(f"   📈 Active Positions: {len(portfolio['positions'])}")
        print(f"   ⚖️  Risk Level: {risk_assessment['risk_level']}")
        print(f"   📊 Options Exposure: {risk_assessment['risk_percentage']:.1f}% of account")

        # Position Details
        if portfolio['positions']:
            print(f"\n📋 CURRENT POSITIONS:")
            for detail in risk_assessment['position_details']:
                print(f"   • {detail['symbol']} {detail['type']} "
                      f"(P&L: {detail['pnl_pct']:+.1f}%, Risk: ${detail['risk']:,.0f})")

        # Risk Alerts
        if risk_assessment['recommendations']:
            print(f"\n⚠️  PORTFOLIO ALERTS:")
            for rec in risk_assessment['recommendations']:
                print(f"   {rec}")

        # Market Summary
        print(f"\n🌍 MARKET SUMMARY:")
        print(f"   📈 SPY Price: ${market_analysis.get('spy_price', 0):.2f}")
        print(f"   📊 VIX Level: {market_analysis.get('vix_level', 0):.2f}")
        print(f"   📈 Trend: {market_analysis.get('trend', 'UNKNOWN')}")
        print(f"   🌊 Volatility: {market_analysis.get('volatility_regime', 'UNKNOWN')}")

        # Main Recommendation
        print(f"\n🎯 TODAY'S RECOMMENDATION:")
        print(f"   🎬 ACTION: {recommendation['action']}")

        if recommendation['action'] in ['BUY', 'SELL']:
            if 'symbol' in recommendation:
                print(f"   📊 SYMBOL: {recommendation['symbol']}")
            if 'strike' in recommendation:
                print(f"   🎯 STRIKE: ${recommendation['strike']}")
            if 'expiration' in recommendation:
                print(f"   📅 EXPIRATION: {recommendation['expiration']}")
            if 'type' in recommendation:
                print(f"   📈 TYPE: {recommendation['type']}")
            if 'quantity' in recommendation:
                print(f"   📊 QUANTITY: {recommendation['quantity']}")
            if 'max_premium' in recommendation:
                print(f"   💰 MAX PREMIUM: ${recommendation['max_premium']:.0f}")

        print(f"\n💡 REASONING:")
        print(f"   {recommendation['reasoning']}")

        if recommendation.get('risk_reward'):
            print(f"\n⚖️  RISK/REWARD:")
            print(f"   {recommendation['risk_reward']}")

        print(f"\n🚨 URGENCY: {recommendation['urgency']}")

        # Enhanced analysis details if available
        if trading_decision:
            print(f"\n🧠 SOPHISTICATED ANALYSIS:")
            print(f"   📊 Market Decision: {trading_decision.action}")
            print(f"   🎯 Strategy: {trading_decision.strategy}")
            print(f"   📈 Confidence: {trading_decision.confidence:.1%}")

        if risk_assessments:
            avg_risk_score = np.mean([ra.risk_score for ra in risk_assessments])
            print(f"   ⚖️  Risk Team Score: {avg_risk_score:.1f}/10")

        print(f"\n💭 PORTFOLIO CONTEXT: {recommendation.get('portfolio_context', 'N/A')}")

        # Beginner Education
        print(f"\n📚 BEGINNER EXPLANATION:")
        print(f"   {recommendation.get('beginner_explanation', 'Continue learning about options trading fundamentals.')}")

        # Trading Tips
        print(f"\n💡 DAILY TRADING TIPS:")
        if recommendation['action'] == 'BUY':
            print(f"   • Only risk money you can afford to lose completely")
            print(f"   • Options lose value over time (time decay)")
            print(f"   • Consider taking profits at 50-100% gain")
            print(f"   • Set a mental stop loss at 50% of premium paid")
            print(f"   • Don't hold options to expiration unless deeply in-the-money")
        elif recommendation['action'] == 'SELL':
            print(f"   • Close positions before they expire worthless")
            print(f"   • Take profits when available - don't be greedy")
            print(f"   • Cut losses quickly to preserve capital")
            print(f"   • Learn from both winning and losing trades")
        else:
            print(f"   • Patience is crucial in options trading")
            print(f"   • Wait for high-probability setups")
            print(f"   • Use this time to study market conditions")
            print(f"   • Quality trades are better than frequent trades")

        # Next Steps
        print(f"\n📋 NEXT STEPS:")
        print(f"   1. Review the recommendation carefully")
        print(f"   2. Check your broker for current option prices")
        print(f"   3. Execute the trade manually through your broker")
        print(f"   4. Update your portfolio file with any new positions")
        print(f"   5. Run this analysis again tomorrow before market open")

        print("\n" + "="*70)
        print(f"📅 Analysis completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🎯 Remember: This is educational analysis, not financial advice")
        print("="*70)

def run_personal_trading():
    """Main function for personal trading mode"""
    system = PersonalTradingSystem()
    system.run_daily_analysis()

# --- MAIN EXECUTION BLOCK ---

if __name__ == "__main__":
    import sys

    # Check if user wants personal trading mode
    if len(sys.argv) > 1 and sys.argv[1] == "personal":
        print("🎯 Starting Personal Trading Mode...")
        run_personal_trading()
    else:
        # Default: Test SteeringConf aggregation algorithms
        logging.info("Testing SteeringConf aggregation algorithms...")
        test_steeringconf_aggregation()
        logging.info("SteeringConf testing complete!")
        print("\n💡 TIP: Run with 'python \"Option Colab.py\" personal' for personal trading mode")